﻿<UserControl x:Class="WpfApp.BomPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10" HorizontalAlignment="Left">
            <Button Content="新增"
                    Command="{Binding AddCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Margin="0,0,10,0"/>
            <Button Content="删除选中"
                    Command="{Binding DeleteSelectedCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0"/>
        </StackPanel>

        <!-- 数据表格区域 - 添加滚动支持 -->
        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto"
                      Margin="10">
            <DataGrid x:Name="bomDataGrid"
                      ItemsSource="{Binding BomProducts}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      SelectionMode="Extended"
                      MinHeight="200"
                      HorizontalAlignment="Stretch"
                      VerticalAlignment="Top">
            <DataGrid.Columns>
                <!-- 多选框列 -->
                <DataGridTemplateColumn Width="50">
                    <DataGridTemplateColumn.Header>
                        <CheckBox x:Name="selectAllCheckBox"
                                  HorizontalAlignment="Center"
                                  Checked="SelectAllCheckBox_Checked"
                                  Unchecked="SelectAllCheckBox_Unchecked"/>
                    </DataGridTemplateColumn.Header>
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                      HorizontalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <!-- 序号列 -->
                <DataGridTemplateColumn Header="序号" Width="60">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding RelativeSource={RelativeSource AncestorType=DataGridRow}, Path=Header}"
                                       HorizontalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <DataGridTextColumn Header="BOM编号" Binding="{Binding BomCode}" Width="120"/>
                <DataGridTextColumn Header="版本号" Binding="{Binding BomVersion}" Width="80"/>
                <DataGridTextColumn Header="产品名称" Binding="{Binding ProductId}" Width="120"/>
                <DataGridTextColumn Header="产品编号" Binding="{Binding ProductCode }" Width="100"/>
                <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" Width="120"/>
                <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="80"/>
                <DataGridTextColumn Header="数量BOM" Binding="{Binding DailyOutput}" Width="100"/>
                <DataGridCheckBoxColumn Header="日产量" Binding="{Binding IsSystemCode}" Width="80"/>

                <!-- 操作列 -->
                <DataGridTemplateColumn Header="操作" Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="编辑"
                                        Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}"
                                        Style="{StaticResource MaterialDesignFlatButton}"
                                        Foreground="Blue"
                                        Margin="2"/>
                                <Button Content="删除"
                                        Command="{Binding DataContext.DeleteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}"
                                        Style="{StaticResource MaterialDesignFlatButton}"
                                        Foreground="Red"
                                        Margin="2"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
            </DataGrid>
        </ScrollViewer>

        <!-- 分页控件 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10">
            <Button Content="上一页" Command="{Binding PrevPageCommand}" Margin="5"/>
            <TextBlock Text="{Binding Page}" Margin="10,0" VerticalAlignment="Center"/>
            <Button Content="下一页" Command="{Binding NextPageCommand}" Margin="5"/>
            <TextBlock Text="总数:" Margin="10,0" VerticalAlignment="Center"/>
            <TextBlock Text="{Binding Total}" Margin="5,0" VerticalAlignment="Center"/>
        </StackPanel>
    </Grid>
</UserControl>