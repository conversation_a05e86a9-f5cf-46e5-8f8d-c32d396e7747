{"runtimeOptions": {"tfm": "net6.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "6.0.0"}, {"name": "Microsoft.WindowsDesktop.App", "version": "6.0.0"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "D:\\DevExpres\\Components\\Offline Packages", "D:\\VisualStudioSetup 2\\NuGetPackages"], "configProperties": {"Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}