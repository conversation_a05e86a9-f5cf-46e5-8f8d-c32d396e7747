﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Security.Policy;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media.Media3D;
using static MaterialDesignThemes.Wpf.Theme.ToolBar;

namespace WpfApp.ProductionSchedu
{
    public class ScheduleViewModel : INotifyPropertyChanged
    {
        private long _workOrderId;
        private string _workOrderCode;
        private string _workOrderName;
        private string _planName;
        private string _planCode;
        private string _productType;
        private string _productName;
        private string _productCode;
        private string _specification;
        private string _productCategory;
        private string _unit;
        private string _bomCode;
        private string _version;
        private int _planNumber;
        private string _bomId;
        private DateTime _planStartTime = DateTime.Now;
        private DateTime _planEndTime = DateTime.Now.AddDays(7);
        private DateTime _demandTime = DateTime.Now.AddDays(5);
        private string _remarks = "";
        private string _processRouteName;
        private string _processRouteCode;
        private string _processRouteId;
        //private int _totalMaterialQuantity;

        public string ProcessRouteId
        {
            get => _processRouteId;
            set
            {
                _processRouteId = value;
                OnPropertyChanged(nameof(ProcessRouteId));
            }
        }

        public string BomId
        {
            get => _bomId;
            set
            {
                _bomId = value;
                OnPropertyChanged(nameof(BomId));
            }
        }

        public long WorkOrderId
        {
            get => _workOrderId;
            set { _workOrderId = value; OnPropertyChanged(nameof(WorkOrderId)); }
        }
        public string WorkOrderCode
        {
            get => _workOrderCode;
            set { _workOrderCode = value; OnPropertyChanged(nameof(WorkOrderCode)); }
        }

        public string ProductCategory
        {
            get => _productCategory;
            set { _productCategory = value; OnPropertyChanged(nameof(ProductCategory)); }
        }

        public string WorkOrderName
        {
            get => _workOrderName;
            set { _workOrderName = value; OnPropertyChanged(nameof(WorkOrderName)); }
        }

        public string PlanName
        {
            get => _planName;
            set { _planName = value; OnPropertyChanged(nameof(PlanName)); }
        }

        public string PlanCode
        {
            get => _planCode;
            set { _planCode = value; OnPropertyChanged(nameof(PlanCode)); }
        }

        public string ProductType
        {
            get => _productType;
            set { _productType = value; OnPropertyChanged(nameof(ProductType)); }
        }

        public string ProductName
        {
            get => _productName;
            set { _productName = value; OnPropertyChanged(nameof(ProductName)); }
        }

        public string ProductCode
        {
            get => _productCode;
            set { _productCode = value; OnPropertyChanged(nameof(ProductCode)); }
        }

        public string Specification
        {
            get => _specification;
            set { _specification = value; OnPropertyChanged(nameof(Specification)); }
        }

        public string Unit
        {
            get => _unit;
            set { _unit = value; OnPropertyChanged(nameof(Unit)); }
        }

        public string BomCode
        {
            get => _bomCode;
            set { _bomCode = value; OnPropertyChanged(nameof(BomCode)); }
        }

        public string Version
        {
            get => _version;
            set { _version = value; OnPropertyChanged(nameof(Version)); }
        }

        public int PlanNumber
        {
            get => _planNumber;
            set { _planNumber = value; OnPropertyChanged(nameof(PlanNumber)); }
        }

        public DateTime PlanStartTime
        {
            get => _planStartTime;
            set { _planStartTime = value; OnPropertyChanged(nameof(PlanStartTime)); }
        }

        public DateTime PlanEndTime
        {
            get => _planEndTime;
            set { _planEndTime = value; OnPropertyChanged(nameof(PlanEndTime)); }
        }

        public DateTime DemandTime
        {
            get => _demandTime;
            set { _demandTime = value; OnPropertyChanged(nameof(DemandTime)); }
        }

        public string Remarks
        {
            get => _remarks;
            set { _remarks = value; OnPropertyChanged(nameof(Remarks)); }
        }

        public string ProcessRouteName
        {
            get => _processRouteName;
            set { _processRouteName = value; OnPropertyChanged(nameof(ProcessRouteName)); }
        }

        public string ProcessRouteCode
        {
            get => _processRouteCode;
            set { _processRouteCode = value; OnPropertyChanged(nameof(ProcessRouteCode)); }
        }

        public ICommand BackCommand { get; }
        public ICommand SubmitCommand { get; }

        public ICommand AddTaskCommand { get; }
        public ICommand EditTaskCommand { get; }
        public ICommand DeleteTaskCommand { get; }

        public event Action BackRequested;

        public ScheduleViewModel(long workOrderId)
        {
            WorkOrderId = workOrderId;
            BackCommand = new RelayCommand(_ => BackRequested?.Invoke());
            //添加提交排产的响应结构
            SubmitCommand = new RelayCommand(async _=>await SubmitScheduleAsync());
            AddTaskCommand = new RelayCommand(_=>ShowAddTaskDialog());
            EditTaskCommand = new RelayCommand(EditTask);
            DeleteTaskCommand = new RelayCommand(async parameter => await DeleteTaskAsync(parameter));
            LoadWorkOrderDetail();
        }
        //新增任务
        private void ShowAddTaskDialog()
        {
            var dialog = new AddTaskDialog();
            var viewModel = new AddTaskViewModel(WorkOrderId)
            {
                ProcessRouteId = ProcessRouteId,
                ProcessStepId = SelectedProcessStepId
            };

            dialog.DataContext = viewModel;

            viewModel.DialogClosed += (success) =>
            {
                dialog.Close();
                if (success)
                {
                    // 刷新工艺步骤列表
                    if (!string.IsNullOrEmpty(ProcessRouteId) && !string.IsNullOrEmpty(SelectedProcessStepId))
                    {
                        LoadProcessSteps(ProcessRouteId, SelectedProcessStepId);
                    }
                }
            };

            dialog.ShowDialog();
        }

        //编辑任务
        private void EditTask(object parameter)
        {
            if (parameter is ProcessStepModel task)
            {
                var dialog = new AddTaskDialog();
                var viewModel = new AddTaskViewModel(WorkOrderId)
                {
                    ProcessRouteId = ProcessRouteId,
                    ProcessStepId = SelectedProcessStepId,
                    TaskCode = task.TaskCode,
                    TaskName = task.TaskName,
                    PlanQuantity = task.PlanQuantity,
                    StartTime = task.PlanStartTime ?? DateTime.Now,
                    EndTime = task.PlanEndTime ?? DateTime.Now.AddDays(1),
                    TaskColorHex = task.TaskColor ?? "#007BFF",
                    IsEditMode = true,
                    TaskId = task.TaskId
                };

                dialog.DataContext = viewModel;

                viewModel.DialogClosed += (success) =>
                {
                    dialog.Close();
                    if (success)
                    {
                        // 刷新工艺步骤列表
                        if (!string.IsNullOrEmpty(ProcessRouteId) && !string.IsNullOrEmpty(SelectedProcessStepId))
                        {
                            LoadProcessSteps(ProcessRouteId, SelectedProcessStepId);
                        }
                    }
                };

                dialog.ShowDialog();
            }
        }

        //删除任务
        private async Task DeleteTaskAsync(object parameter)
        {
            if (parameter is ProcessStepModel task)
            {
                // 先检查TaskId是否为空
                if (string.IsNullOrEmpty(task.TaskId))
                {
                    System.Windows.MessageBox.Show("任务ID为空，无法删除", "错误");
                    return;
                }

                var result = System.Windows.MessageBox.Show(
                    $"确定要删除任务 '{task.TaskName}' 吗？\nTaskId: {task.TaskId}",
                    "确认删除",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    try
                    {
                        using var client = new HttpClient();
                        if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                        {
                            client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                        }

                        var url = $"http://localhost:5005/api/task/delTask?taskId={task.TaskId}";
                        System.Diagnostics.Debug.WriteLine($"删除请求URL: {url}");

                        // 使用DELETE方法而不是POST
                        var response = await client.DeleteAsync(url);

                        System.Diagnostics.Debug.WriteLine($"删除响应状态码: {response.StatusCode}");

                        var responseContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"删除响应内容: {responseContent}");

                        if (response.IsSuccessStatusCode)
                        {
                            System.Windows.MessageBox.Show("任务删除成功！", "成功");
                            // 刷新工艺步骤列表
                            if (!string.IsNullOrEmpty(ProcessRouteId) && !string.IsNullOrEmpty(SelectedProcessStepId))
                            {
                                LoadProcessSteps(ProcessRouteId, SelectedProcessStepId);
                            }
                        }
                        else
                        {
                            System.Windows.MessageBox.Show($"删除失败：{response.StatusCode}\n详细信息：{responseContent}", "错误");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"删除任务异常: {ex.Message}");
                        System.Windows.MessageBox.Show($"删除任务时发生错误：{ex.Message}", "错误");
                    }
                }
            }
        }
        //物料
        private ObservableCollection<MaterialModel> _materials = new ObservableCollection<MaterialModel>();
        private int _totalMaterialQuantity;

        public ObservableCollection<MaterialModel> Materials
        {
            get => _materials;
            set { _materials = value; OnPropertyChanged(nameof(Materials)); }
        }

        public int TotalMaterialQuantity
        {
            get => _totalMaterialQuantity;
            set { _totalMaterialQuantity = value; OnPropertyChanged(nameof(TotalMaterialQuantity)); }
        }


        // 加载工单信息
        private async void LoadWorkOrderDetail()
        {
            try
            {
                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var response = $"http://localhost:5005/api/workOrder/detail?workOrderId={WorkOrderId}";
                var json = await client.GetStringAsync(response);

                // 先打印原始JSON查看数据结构
                System.Diagnostics.Debug.WriteLine($"API返回的JSON: {json}");

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var result = JsonSerializer.Deserialize<WorkOrderDetailResponse>(json, options);

                System.Diagnostics.Debug.WriteLine($"反序列化结果 - Code: {result?.Code}, Message: {result?.Message}, Result是否为null: {result?.Result == null}");
                if (result?.Result != null)
                {
                    var detail = result.Result;
                    WorkOrderCode = detail.WorkOrderCode;
                    WorkOrderName = detail.WorkOrderName;
                    PlanName = detail.PlanName;
                    PlanCode = detail.PlanCode;
                    ProductCode = detail.ProductCode;
                    ProductType = detail.ProductType;
                    ProductName = detail.ProductName;
                    Specification = detail.Specification;
                    Unit = detail.Unit;
                    BomCode = detail.BomCode; // 注意这里是BomVersion
                    Version = detail.BomVersion;
                    ProcessRouteCode = detail.ProcessRouteCode;
                    ProcessRouteName = detail.ProcessRouteName;
                    BomId = detail.BomId;
                    ProcessRouteId = detail.ProcessRouteId;

                    // 处理时间字段
                    if (DateTime.TryParse(detail.PlanStartTime, out var startTime))
                        PlanStartTime = startTime;
                    if (DateTime.TryParse(detail.PlanEndTime, out var endTime))
                        PlanEndTime = endTime;
                    if (DateTime.TryParse(detail.DemandTime, out var demandTime))
                        DemandTime = demandTime;

                    if (detail.PlanNumber.HasValue)
                        PlanNumber = detail.PlanNumber.Value;

                    Remarks = detail.Remarks ?? "";

                    // 加载物料清单
                    if (!string.IsNullOrEmpty(detail.BomId))
                    {
                        LoadMaterialList(detail.BomId);
                    }
                    // 加载工艺步骤 - 这里需要从工单详情中获取工艺路线ID和工序ID
                    // 假设从detail中可以获取到这些信息
                    if (!string.IsNullOrEmpty(detail.ProcessRouteId))
                    {
                        // 直接使用API返回的工序信息
                        if (!string.IsNullOrEmpty(detail.ProcessSteps))
                        {
                            var stepIds = detail.ProcessSteps.Split(',', StringSplitOptions.RemoveEmptyEntries);
                            ProcessStepOptions.Clear();
                            foreach (var stepId in stepIds)
                            {
                                ProcessStepOptions.Add(stepId.Trim());
                            }

                            // 默认选择第一个工序
                            if (ProcessStepOptions.Count > 0)
                            {
                                SelectedProcessStepId = ProcessStepOptions.First();
                                LoadProcessSteps(detail.ProcessRouteId, ProcessStepOptions.First());
                            }
                        }
                        else
                        {
                            // 如果没有工序信息，使用原来的方法
                            LoadAvailableProcessSteps(detail.ProcessRouteId);
                        }
                    }
                }

                //获取物料列表
                var materialResponse = $"";
            }
            catch (Exception)
            {

                throw;
            }
        }


        //加载物料清单
        private async void LoadMaterialList(string bomId)
        {
            try
            {
                using var client = new HttpClient();
                if(!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var url = $"http://localhost:5005/api/workOrder/materialEntityList?Page=1&PageSize=100&bomId={bomId}";
                var json = await client.GetStringAsync(url);

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var result = JsonSerializer.Deserialize<MaterialListResponse>(json, options);

                if (result?.Result?.Items != null)
                {
                    var materials = new List<MaterialModel>();
                    for (int i = 0; i < result.Result.Items.Count; i++)
                    {
                        var item = result.Result.Items[i];
                        materials.Add(new MaterialModel
                        {
                            Index = i + 1,
                            MaterialCode = item.MaterialCode,
                            MaterialName = item.MaterialName,
                            Specification = item.Specification,
                            Unit = item.Unit,
                            RequiredQuantity = item.UsageQuantity,
                            UsageRatio = item.UsageRatio
                        });
                    }
                    Materials = new ObservableCollection<MaterialModel>(materials);
                    TotalMaterialQuantity = (int)materials.Sum(m => m.RequiredQuantity);
                }
            }
            catch (Exception)
            {

                throw;
            }
        }


        //加载工序步骤
        private ObservableCollection<ProcessStepModel> _processSteps = new ObservableCollection<ProcessStepModel>();

        //不同工序显示不同的步骤，可以添加工序选择功能
        private string _selectedProcessStepId = "1";
        public string SelectedProcessStepId
        {
            get => _selectedProcessStepId;
            set
            {
                if (_selectedProcessStepId != value)
                {
                    _selectedProcessStepId = value;
                    OnPropertyChanged(nameof(SelectedProcessStepId));
                    // 当工序改变时重新加载步骤
                    if (!string.IsNullOrEmpty(ProcessRouteId) && !string.IsNullOrEmpty(value))
                    {
                        LoadProcessSteps(ProcessRouteId, value);
                    }
                }
            }
        }
        public ObservableCollection<string> ProcessStepOptions { get; } = new ObservableCollection<string> { "1", "2", "3", "4", "5" };

        public ObservableCollection<ProcessStepModel> ProcessSteps
        {
            get
            {
                return _processSteps;
            }
            set
            {
                _processSteps = value;
                OnPropertyChanged(nameof(ProcessSteps));
            }
        }

        private async void LoadProcessSteps(string processRouteId, string processStepId)
        {
            try
            {
                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var url = $"http://localhost:5005/api/task/taskSitesProcessRoute?ProcessRouteId={processRouteId}&ProcessStepId={processStepId}";

                System.Diagnostics.Debug.WriteLine($"请求工艺步骤URL:{url}");

                var json = await client.GetStringAsync(url);
                System.Diagnostics.Debug.WriteLine($"返回数据:{json}");

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var result = JsonSerializer.Deserialize<ProcessStepResponse>(json, options);

                if (result?.Result != null && result.Result.Count > 0)
                {
                    var processSteps = new List<ProcessStepModel>();

                    for (int i = 0; i < result.Result.Count; i++)
                    {
                        var item = result.Result[i];

                        // 调试输出每个字段的值
                        System.Diagnostics.Debug.WriteLine($"Item {i}: TaskCode={item.TaskCode}, TaskName={item.TaskName},Id={item.Id}");

                        // 安全地转换PlanQuantity
                        int planQty = 0;
                        if (item.PlanQuantity != null)
                        {
                            if (item.PlanQuantity is int intValue)
                                planQty = intValue;
                            else if (int.TryParse(item.PlanQuantity.ToString(), out var parsedValue))
                                planQty = parsedValue;
                        }

                        processSteps.Add(new ProcessStepModel
                        {
                            Index = i + 1,
                            TaskId = item.Id,
                            TaskCode = item.TaskCode ?? $"TASK{i + 1:D3}", // 如果为空则生成默认值
                            TaskName = item.TaskName ?? "未命名任务",
                            SiteName = item.SiteName ?? "",
                            SiteCode = item.SiteCode ?? "",
                            PlanQuantity = planQty,
                            PlanStartTime = item.StartTime,
                            PlanEndTime = item.EndTime,
                            TaskColor = item.TaskColor ?? ""
                        });
                    }

                    ProcessSteps = new ObservableCollection<ProcessStepModel>(processSteps);
                    System.Diagnostics.Debug.WriteLine($"加载了 {processSteps.Count} 个工艺步骤");
                }
                else
                {
                    ProcessSteps = new ObservableCollection<ProcessStepModel>();
                    System.Diagnostics.Debug.WriteLine($"工艺路线ID:{processRouteId}, 工序ID:{processStepId} 没有找到工艺步骤数据");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载工艺步骤异常: {ex.Message}");
                System.Windows.MessageBox.Show($"加载工艺步骤失败: {ex.Message}");
            }
        }
        // 获取工艺路线下的所有工序
        private async void LoadAvailableProcessSteps(string processRouteId)
        {
            try
            {
                // 尝试获取工序1-5的数据，看哪些有数据
                var availableSteps = new List<string>();

                for (int i = 1; i <= 5; i++)
                {
                    using var client = new HttpClient();
                    if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                    {
                        client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                    }

                    var url = $"http://localhost:5005/api/task/taskSitesProcessRoute?ProcessRouteId={processRouteId}&ProcessStepId={i}";
                    var json = await client.GetStringAsync(url);

                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                    var result = JsonSerializer.Deserialize<ProcessStepResponse>(json, options);

                    if (result?.Result != null && result.Result.Count > 0)
                    {
                        availableSteps.Add(i.ToString());
                    }
                }

                if (availableSteps.Count > 0)
                {
                    // 更新可用工序列表
                    ProcessStepOptions.Clear();
                    foreach (var step in availableSteps)
                    {
                        ProcessStepOptions.Add(step);
                    }

                    // 默认选择第一个可用工序并加载其数据
                    SelectedProcessStepId = availableSteps.First();
                    LoadProcessSteps(processRouteId, availableSteps.First());
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"工艺路线 {processRouteId} 下没有找到任何工序数据");
                    ProcessSteps = new ObservableCollection<ProcessStepModel>();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载可用工序失败: {ex.Message}");
            }
        }


        //提交排产
        private async Task SubmitScheduleAsync()
        {
            try
            {
                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var scheduleData = new
                {
                    workOrderId = WorkOrderId,
                    PlanEndTime = PlanEndTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    PlanStartTime = PlanStartTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    DemandTime = DemandTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    PlanNumber = PlanNumber,
                    Remarks = Remarks ?? ""
                };

                var json = JsonSerializer.Serialize(scheduleData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // 修正API端点名称
                var url = $"http://localhost:5005/api/workOrder/productSchedu?workOrderId={scheduleData.workOrderId}&PlanEndTime={scheduleData.PlanEndTime}&PlanStartTime={scheduleData.PlanStartTime}&DemandTime={scheduleData.DemandTime}&PlanNumber={scheduleData.PlanNumber}&Remarks={scheduleData.Remarks}";

                System.Diagnostics.Debug.WriteLine($"请求URL: {url}");
                System.Diagnostics.Debug.WriteLine($"请求数据: {json}");

                var response = await client.PutAsync(url, content);

                System.Diagnostics.Debug.WriteLine($"响应状态码: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ScheduleResponse>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    if (result?.Code == 200)
                    {
                        System.Windows.MessageBox.Show("排产提交成功！", "成功", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                        BackRequested?.Invoke();
                    }
                    else
                    {
                        System.Windows.MessageBox.Show($"排产提交失败：{result?.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"错误响应内容: {errorContent}");
                    System.Windows.MessageBox.Show($"请求失败：{response.StatusCode}\n详细信息：{errorContent}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                System.Windows.MessageBox.Show($"提交排产时发生错误：{ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }


        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
    public class WorkOrderDetailResponse
    {
        public int Code { get; set; }
        public string Message { get; set; }
        public WorkOrderDetail Result { get; set; }
    }
    public class WorkOrderDetail
    {

        public string? BomId { get; set; }
        public string? WorkOrderCode { get; set; }
        public string? WorkOrderName { get; set; }
        public string? PlanId { get; set; }
        public string? PlanName { get; set; }
        public string? PlanCode { get; set; }
        public string? SourceId { get; set; }
        public string? SourceName { get; set; }
        public string? ProductCode { get; set; }
        public string? ProductName { get; set; }
        public string? Specification { get; set; }
        public string? Unit { get; set; }
        public string? ProductType { get; set; }
        public string? BomCode { get; set; }
        public string? BomVersion { get; set; }
        public string? PlanStartTime { get; set; }
        public string? PlanEndTime { get; set; }
        public string? DemandTime { get; set; }
        public int? PlanNumber { get; set; }
        public string? Remarks { get; set; }
        public int[]? MaterialIds { get; set; }
        public string? ProcessRouteCode { get; set; }
        public string? ProcessRouteName { get; set; }
        public object? Extras { get; set; }
        public string? Time { get; set; }
        public string? ProcessRouteId { get; set; }
        public string? ProcessSteps {  get; set; }
    }

    public class MaterialListResponse
    {
        public int Code { get; set; }
        public string Message { get; set; }
        public MaterialListResult Result { get; set; }
    }

    public class MaterialListResult
    {
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int Total { get; set; }
        public int TotalPages { get; set; }
        public List<MaterialItem> Items { get; set; }
    }
    public class FlexibleDateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var dateString = reader.GetString();
            if (string.IsNullOrEmpty(dateString))
                return DateTime.MinValue;

            // 尝试多种日期格式
            var formats = new[]
            {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-ddTHH:mm:ss",
            "yyyy-MM-ddTHH:mm:ss.fff",
            "yyyy-MM-ddTHH:mm:ss.fffZ",
            "yyyy-MM-dd",
            "MM/dd/yyyy",
            "dd/MM/yyyy"
        };

            foreach (var format in formats)
            {
                if (DateTime.TryParseExact(dateString, format, null, DateTimeStyles.None, out var result))
                    return result;
            }

            // 如果所有格式都失败，尝试默认解析
            if (DateTime.TryParse(dateString, out var defaultResult))
                return defaultResult;

            return DateTime.MinValue;
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString("yyyy-MM-dd HH:mm:ss"));
        }
    }

    public class MaterialItem
    {
        public string MaterialCode { get; set; }
        public string MaterialName { get; set; }
        public string Specification { get; set; }
        public string Unit { get; set; }
        public string MaterialCategory { get; set; }
        public bool IsEnabled { get; set; }

        [JsonIgnore]
        public string LeadDays { get; set; }

        [JsonIgnore]
        public string StockUpperLimit { get; set; }

        [JsonIgnore]
        public string StockLowerLimit { get; set; }

        public decimal UsageQuantity { get; set; }
        public decimal UsageRatio { get; set; }

        [JsonConverter(typeof(FlexibleDateTimeConverter))]
        public DateTime CreateTime { get; set; }

        [JsonConverter(typeof(FlexibleDateTimeConverter))]
        public DateTime UpdateTime { get; set; }
    }

    //添加工艺步骤相关的响应模型
    public class ProcessStepResponse
    {
        public int Code { get; set; }
        public string Message { get; set; }
        public List<ProcessStepItem> Result { get; set; }  // 直接是数组，不是对象
    }
    public class ProcessStepItem
    {
        [JsonPropertyName("taskName")]
        public string TaskName { get; set; }

        [JsonPropertyName("taskCode")]
        public string TaskCode { get; set; }

        [JsonPropertyName("processRouteCode")]
        public string ProcessRouteCode { get; set; }

        [JsonPropertyName("processRouteName")]
        public string ProcessRouteName { get; set; }

        [JsonPropertyName("siteName")]
        public string SiteName { get; set; }

        [JsonPropertyName("siteCode")]
        public string SiteCode { get; set; }

        [JsonPropertyName("planQuantity")]
        public object PlanQuantity { get; set; }

        [JsonPropertyName("startTime")]
        [JsonConverter(typeof(FlexibleDateTimeConverter))]
        public DateTime StartTime { get; set; }

        [JsonPropertyName("endTime")]
        [JsonConverter(typeof(FlexibleDateTimeConverter))]
        public DateTime EndTime { get; set; }

        [JsonPropertyName("taskColor")]
        public string TaskColor { get; set; }

        [JsonPropertyName("createTime")]
        public string CreateTime { get; set; }

        [JsonPropertyName("updateTime")]
        public string UpdateTime { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    //添加提交排产的响应模型
    public class ScheduleResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public object Result { get; set; }
    }
}
