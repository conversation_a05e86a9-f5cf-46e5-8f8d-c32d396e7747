﻿// 引用系统基础类库
using System;
// 引用泛型集合类库
using System.Collections.Generic;
// 引用LINQ查询类库
using System.Linq;
// 引用文本处理类库
using System.Text;
// 引用异步任务类库
using System.Threading.Tasks;
// 引用WPF窗口类库
using System.Windows;
// 引用WPF控件类库
using System.Windows.Controls;
// 引用WPF数据绑定类库
using System.Windows.Data;
// 引用WPF文档类库
using System.Windows.Documents;
// 引用WPF输入类库
using System.Windows.Input;
// 引用WPF媒体类库
using System.Windows.Media;
// 引用WPF图像类库
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
// 引用WPF形状类库
using System.Windows.Shapes;
using WpfApp.Plan;
using WpfApp.Procedure;
using WpfApp.ProcessRoute;
using WpfApp.WorkOrder;
using WpfApp.WorkOrderDetail;
using WpfApp.WorkTask;

// 定义主应用程序命名空间
namespace WpfApp
{
    /// <summary>
    /// MenuPage.xaml 的交互逻辑
    /// </summary>
    public partial class MenuPage : UserControl
    {
        /// <summary>
        /// 构造函数 - 初始化菜单页面
        /// </summary>
        private BomViewModel _currentBomViewModel;

        public MenuPage()
        {
            // 初始化XAML定义的组件
            InitializeComponent();
            // 创建菜单页面视图模型实例
            this.DataContext = new MenuPageViewModel();
            
            // 注册菜单变更事件处理器
            if (this.DataContext is MenuPageViewModel viewModel)
            {
                viewModel.MenuChanged += OnMenuChanged;
            }
        }

        private void OnMenuChanged(string menu)
        {
            switch (menu)
            {
                case "BOM":
                    ShowBomPage();
                    break;

                case "生产工单":
                    var workOrderPage = new WorkOrderPage();
                    workOrderPage.ScheduleRequested += OnScheduleRequested;
                    workOrderPage.ViewRequested += OnViewWorkOrderREquested;
                    MenuContent.Content = workOrderPage;
                    break;
                case "生产计划":
                    var planPage = new PlanPage();
                    var planViewModel = new PlanViewModel();
                    // 订阅新增计划请求
                    planViewModel.AddPlanRequested += OnAddPlanRequested;
                    // 订阅页面导航事件
                    planViewModel.NavigateToPage += OnNavigateToPage;
                    planPage.DataContext = planViewModel;
                    MenuContent.Content = planPage;
                    break;
                case "工单任务":
                    MenuContent.Content = new ShowTaskPage();
                    break;
                case "工序管理":
                    MenuContent.Content = new ProcedurePage();
                    break;
                case "工艺路线":
                    MenuContent.Content = new ProcessRouteDisplayPage();
                    break;
                case "新增工艺路线":
                    // 创建AddProcessRouteWindow并提取其内容
                    var addWindow = new AddProcessRouteWindow();
                    var content = addWindow.Content;
                    if (content != null)
                    {
                        // 从Window中移除内容
                        addWindow.Content = null;
                        // 创建UserControl包装内容
                        var userControl = new UserControl();
                        userControl.Content = content;
                        userControl.DataContext = new ProcessRoute.AddProcessRouteViewModel();
                        MenuContent.Content = userControl;
                    }
                    break;
                case "首页":
                    MenuContent.Content = new TextBlock { Text = "首页内容", FontSize = 32, VerticalAlignment = VerticalAlignment.Center, HorizontalAlignment = HorizontalAlignment.Center };
                    break;
                // 可以继续添加其他有具体页面的菜单case
                default:
                    // 没有对应页面的菜单显示菜单名称
                    MenuContent.Content = new TextBlock {
                        Text = menu,
                        FontSize = 32,
                        VerticalAlignment = VerticalAlignment.Center,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };
                    break;
            }
        }

        private void ShowBomPage()
        {
            _currentBomViewModel = new BomViewModel();
            var bomPage = new BomPage { DataContext = _currentBomViewModel };

            // 订阅导航事件
            _currentBomViewModel.NavigateToEdit += OnNavigateToEdit;

            MenuContent.Content = bomPage;
        }

        private void OnNavigateToEdit(BomEditViewModel editViewModel)
        {
            var editPage = new BomEditPage(editViewModel);

            // 订阅返回和保存事件
            editPage.NavigateBack += OnNavigateBackToBom;
            editPage.ProductSaved += OnProductSaved;

            MenuContent.Content = editPage;
        }

        private void OnProductSaved(BomProduct savedProduct)
        {
            // 如果是新增，添加到列表
            if (_currentBomViewModel != null)
            {
                _currentBomViewModel.AddNewProduct(savedProduct);
            }
        }

        private void OnNavigateBackToBom()
        {
            // 返回BOM列表页面，重用当前的ViewModel以保持数据
            if (_currentBomViewModel != null)
            {
                var bomPage = new BomPage { DataContext = _currentBomViewModel };
                _currentBomViewModel.NavigateToEdit += OnNavigateToEdit;
                MenuContent.Content = bomPage;
            }
            else
            {
                ShowBomPage();
            }
        }

        private void OnScheduleRequested(long workOrderId)
        {
            // 跳转到排产页面
            var schedulePage = new ProductionSchedu.SchedulePage(workOrderId);
            var viewModel = new ProductionSchedu.ScheduleViewModel(workOrderId);
            schedulePage.DataContext = viewModel;

            // 订阅返回事件
            viewModel.BackRequested += () =>
            {
                // 返回到工单页面
                OnMenuChanged("生产工单");
            };

            MenuContent.Content = schedulePage;
        }

        private void OnViewWorkOrderREquested(WorkOrderModel workOrder)
        {
            //跳转工单详情页面
            var detailPage = new WorkOrderDetailPage(workOrder);
            //订阅返回事件
            if (detailPage.DataContext is WorkOrderDetailViewModel viewModel)
            {
                viewModel.BackRequested += () =>
                {
                    OnMenuChanged("生产工单");
                };
            }
            MenuContent.Content = detailPage;
        }

        // 处理生产计划新增请求
        public void OnAddPlanRequested()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始处理新增计划请求 ===");

                // 创建编辑计划页面和ViewModel
                var editPlanViewModel = new EditPlanViewModel(isNewPlan: true, backAction: () =>
                {
                    // 返回到生产计划页面
                    OnMenuChanged("生产计划");
                });

                var editPlanPage = new EditPlanPage();
                editPlanPage.DataContext = editPlanViewModel;

                // 显示编辑计划页面
                MenuContent.Content = editPlanPage;

                System.Diagnostics.Debug.WriteLine("=== 新增计划页面已显示 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理新增计划请求时发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"打开新增计划页面时出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 处理页面导航
        private void OnNavigateToPage(UserControl page)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始处理页面导航 ===");
                System.Diagnostics.Debug.WriteLine($"导航到页面类型: {page?.GetType().Name}");

                MenuContent.Content = page;

                System.Diagnostics.Debug.WriteLine("=== 页面导航完成 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"页面导航时发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"页面导航时出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
