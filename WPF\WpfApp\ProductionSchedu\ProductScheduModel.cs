using System;
using System.Collections.Generic;

namespace WpfApp.ProductionSchedu
{
    public class ProductScheduModel
    {
        /// <summary>
        /// 工单ID
        /// </summary>
        public long WorkOrderId { get; set; }

        /// <summary>
        /// 工单编号
        /// </summary>
        public string? WorkOrderCode { get; set; }

        /// <summary>
        /// 工单名称
        /// </summary>
        public string? WorkOrderName { get; set; }

        /// <summary>
        /// 关联计划名称
        /// </summary>
        public string? PlanName { get; set; }

        /// <summary>
        /// 计划编号
        /// </summary>
        public string? PlanCode { get; set; }

        /// <summary>
        /// 产品类型
        /// </summary>
        public string? ProductType { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        public string? ProductCode { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 产品类别
        /// </summary>
        public string? ProductCategory { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// BOM编号
        /// </summary>
        public string? BomCode { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        public string? Version { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        public int? PlanNumber { get; set; }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        public DateTime? PlanStartTime { get; set; }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        public DateTime? PlanEndTime { get; set; }

        /// <summary>
        /// 需求日期
        /// </summary>
        public DateTime? DemandTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 物料清单
        /// </summary>
        public List<MaterialModel> Materials { get; set; } = new List<MaterialModel>();

        public long BomId {  get; set; }
        /// <summary>
        /// 物料总量
        /// </summary>
        public decimal TotalMaterialQuantity { get; set; }

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        public string? ProcessRouteName { get; set; }

        /// <summary>
        /// 工艺路线编号
        /// </summary>
        public string? ProcessRouteCode { get; set; }
        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public string? ProcessRouteId {  get; set; }

        /// <summary>
        /// 工艺步骤
        /// </summary>
        public List<ProcessStepModel> ProcessSteps { get; set; } = new List<ProcessStepModel>();
    }

    /// <summary>
    /// 物料模型
    /// </summary>
    public class MaterialModel
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string? MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string? MaterialName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 需求使用量
        /// </summary>
        public decimal RequiredQuantity { get; set; }

        /// <summary>
        /// 用料比例
        /// </summary>
        public decimal UsageRatio { get; set; }
    }

    /// <summary>
    /// 工艺步骤模型
    /// </summary>
    public class ProcessStepModel
    {

        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 任务编号
        /// </summary>
        public string? TaskCode { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string? SiteName { get; set; }

        /// <summary>
        /// 站点编号
        /// </summary>
        public string? SiteCode { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        public int PlanQuantity { get; set; }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        public DateTime? PlanStartTime { get; set; }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        public DateTime? PlanEndTime { get; set; }

        /// <summary>
        /// 任务颜色
        /// </summary>
        public string? TaskColor { get; set; }
    }
}