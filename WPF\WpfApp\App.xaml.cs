using System.Configuration;
using System.Data;
using System.Windows;

namespace WpfApp
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {

//        protected override void OnStartup(StartupEventArgs e)
//        {
//            base.OnStartup(e);

//#if DEBUG
//            // 启用WPF调试工具
//            System.Diagnostics.PresentationTraceSources.SetTraceLevel(this, System.Diagnostics.PresentationTraceLevel.High);

//            // 启用Live Visual Tree
//           // System.Windows.Diagnostics.VisualDiagnostics.StartDiagnosticMode();
//#endif
//        }
    }

}
