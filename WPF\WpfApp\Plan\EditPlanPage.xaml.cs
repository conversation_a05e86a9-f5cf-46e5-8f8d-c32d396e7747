using System.Windows.Controls;

namespace WpfApp.Plan
{
    /// <summary>
    /// EditPlanPage.xaml 的交互逻辑
    /// </summary>
    public partial class EditPlanPage : UserControl
    {
        public EditPlanPage()
        {
            InitializeComponent();
        }

        // 添加便利构造函数，直接接收ViewModel
        public EditPlanPage(EditPlanViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }

        private void UploadAttachment_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            // 调用EditPlanViewModel的UploadAttachmentCommand
            if (this.DataContext is EditPlanViewModel vm)
            {
                vm.UploadAttachmentCommand?.Execute(null);
            }
        }
    }
} 