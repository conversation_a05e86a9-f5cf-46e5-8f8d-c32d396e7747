﻿<UserControl x:Class="WpfApp.WorkOrderDetail.WorkOrderDetailPage"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp.WorkOrderDetail"
        mc:Ignorable="d"
        d:DesignHeight="800" d:DesignWidth="1400"
        >
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#F8F9FA" Padding="20,15" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="{Binding WorkOrderName}" FontSize="24" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding WorkOrderCode}" FontSize="16" Foreground="#6C757D" Margin="15,0,0,0" VerticalAlignment="Center"/>
                    <Border Background="#17A2B8" CornerRadius="12" Padding="8,4" Margin="15,0,0,0">
                        <TextBlock Text="{Binding StatusText}" Foreground="White" FontSize="12"/>
                    </Border>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="创建人：" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding CreateUser}" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <TextBlock Text="创建时间：" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding CreateTime}" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        <!-- 工单进度 -->
        <Border Grid.Row="1" Background="White" Padding="20,15" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel>
                <TextBlock Text="工单进度：" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 进度条 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Border Background="#20C997" Width="100" Height="30" CornerRadius="4" Margin="0,0,2,0">
                            <TextBlock Text="100%" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White" FontWeight="Bold"/>
                        </Border>
                        <Border Background="#E83E8C" Width="50" Height="30" CornerRadius="4" Margin="0,0,2,0">
                            <TextBlock Text="20%" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White" FontWeight="Bold"/>
                        </Border>
                        <Border Background="#17A2B8" Width="50" Height="30" CornerRadius="4" Margin="0,0,2,0">
                            <TextBlock Text="10%" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White" FontWeight="Bold"/>
                        </Border>
                        <Border Background="#FD7E14" Width="75" Height="30" CornerRadius="4">
                            <TextBlock Text="30%" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="White" FontWeight="Bold"/>
                        </Border>
                    </StackPanel>

                    <Button Grid.Column="1" Content="查看甘特图" Background="#007BFF" Foreground="White" 
                            Padding="15,8" BorderThickness="0" />
                </Grid>
            </StackPanel>
        </Border>
        <!-- 操作按钮 -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="20,10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Content="暂停" Background="#FFC107" Foreground="White" Padding="12,6" Margin="0,0,10,0" BorderThickness="0" />
                <Button Content="完成" Background="#28A745" Foreground="White" Padding="12,6" Margin="0,0,10,0" BorderThickness="0" />
                <Button Content="关闭" Background="#DC3545" Foreground="White" Padding="12,6" Margin="0,0,10,0" BorderThickness="0" />
                <Button Content="打印" Background="#17A2B8" Foreground="White" Padding="12,6" Margin="0,0,10,0" BorderThickness="0" />
                <Button Content="关闭" Background="#6C757D" Foreground="White" Padding="12,6" 
                        Command="{Binding BackCommand}" BorderThickness="0"/>
            </StackPanel>
        </Border>
        
        <!--详情内容-->
        <TabControl Grid.Row="3" Margin="20">
            <!--基础信息-->
            <TabItem Header="基础信息">
                <ScrollViewer>
                    <Grid Margin="20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <!-- 第一行 -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="工单编号" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding WorkOrderCode}" Background="#F8F9FA" Padding="10" />
                        </StackPanel>
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="工单名称" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding WorkOrderName}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <!-- 第二行 -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="关联计划" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding PlanName}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="计划编号" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding PlanCode}" Background="#F8F9FA" Padding="10" />
                        </StackPanel>
                        <!-- 第三行 -->
                        <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="类型" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding ProductType}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <StackPanel Grid.Row="2" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="订单编号" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding OrderNumber}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <!-- 第四行 -->
                        <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="产品名称" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding ProductName}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <StackPanel Grid.Row="3" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="产品编号" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding ProductCode}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <!-- 第五行 -->
                        <StackPanel Grid.Row="4" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="规格型号" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding Specification}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <StackPanel Grid.Row="4" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="产品类型" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding ProductType}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <!-- 第六行 -->
                        <StackPanel Grid.Row="5" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="单位" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding Unit}" Background="#F8F9FA" Padding="10" />
                        </StackPanel>
                        <StackPanel Grid.Row="5" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="BOM编号" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding BomCode}" Background="#F8F9FA" Padding="10" />
                        </StackPanel>
                        <!-- 第七行 -->
                        <StackPanel Grid.Row="6" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="计划数量" FontWeight="Bold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal" Background="#F8F9FA">
                                <TextBlock Text="{Binding PlanNumber}"/>
                                <Button Content="产品入库" Background="#28A745" Foreground="White" Padding="8,4" Margin="10,0,0,0" BorderThickness="0" />
                            </StackPanel>
                        </StackPanel>
                        <StackPanel Grid.Row="6" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="实际生产数量" FontWeight="Bold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal" Background="#F8F9FA" >
                                <TextBlock Text="{Binding RealityNumber}"/>
                                <TextBlock Text="实际生产数量：关键生产工序，合格数量" Foreground="#6C757D" Margin="10,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                        <!-- 第八行 -->
                        <StackPanel Grid.Row="7" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="计划开工时间" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding PlanStartTime}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <StackPanel Grid.Row="7" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="计划完工时间" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding PlanEndTime}" Background="#F8F9FA" Padding="10" />
                        </StackPanel>
                        <!-- 第九行 -->
                        <StackPanel Grid.Row="8" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="实际开工时间" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding RealityStartTime}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>
                        <StackPanel Grid.Row="8" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="实际完工时间" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding RealityEndTime}" Background="#F8F9FA" Padding="10"/>
                        </StackPanel>

                        <!-- 第十行 -->
                        <StackPanel Grid.Row="9" Grid.Column="0" Margin="0,0,20,15">
                            <TextBlock Text="生产批次" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="0235" Background="#F8F9FA" Padding="10" />
                        </StackPanel>
                        <StackPanel Grid.Row="9" Grid.Column="1" Margin="0,0,0,15">
                            <TextBlock Text="需求日期" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding DemandTime}" Background="#F8F9FA" Padding="10" />
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>
            <!-- 其他标签页 -->
            <TabItem Header="物料清单">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="物料清单" FontSize="16" FontWeight="Bold" 
                   Foreground="#FFC107" Margin="0,0,0,10"/>

                    <DataGrid Grid.Row="1" ItemsSource="{Binding Materials}" AutoGenerateColumns="False" 
                  CanUserAddRows="False" CanUserDeleteRows="False" 
                  GridLinesVisibility="All" HeadersVisibility="All"
                  AlternatingRowBackground="#F8F9FA" RowHeight="35">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60" IsReadOnly="True"/>
                            <DataGridTextColumn Header="物料编号" Binding="{Binding MaterialCode}" Width="120" IsReadOnly="True"/>
                            <DataGridTextColumn Header="物料名称" Binding="{Binding MaterialName}" Width="150" IsReadOnly="True"/>
                            <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" Width="120" IsReadOnly="True"/>
                            <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="80" IsReadOnly="True"/>
                            <DataGridTextColumn Header="需求使用量" Binding="{Binding RequiredQuantity}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="用料比例" Binding="{Binding UsageRatio}" Width="100" IsReadOnly="True"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <TextBlock Grid.Row="2" HorizontalAlignment="Right" Margin="0,10,0,0">
            <Run Text="物料总量: "/>
            <Run Text="{Binding TotalMaterialQuantity}" FontWeight="Bold"/>
                    </TextBlock>
                </Grid>
            </TabItem>
            <TabItem Header="领料记录">
                <TextBlock Text="领料记录内容" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
            </TabItem>
            <TabItem Header="工序任务">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="工序任务" FontSize="16" FontWeight="Bold" 
                   Foreground="#FFC107" Margin="0,0,0,10"/>

                    <!-- 工艺路线信息和工序选择 -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="150"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="工艺路线名称:" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" Text="{Binding ProcessRouteName}" VerticalAlignment="Center" FontWeight="Bold"/>

                        <TextBlock Grid.Column="3" Text="工艺路线编号:" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="4" Text="{Binding ProcessRouteCode}" VerticalAlignment="Center"/>

                        <TextBlock Grid.Column="6" Text="选择工序:" VerticalAlignment="Center"/>
                        <ComboBox Grid.Column="7" ItemsSource="{Binding ProcessStepOptions}" 
                      SelectedItem="{Binding SelectedProcessStepId}" Height="30"/>
                    </Grid>

                    <!-- 工序任务列表 -->
                    <DataGrid Grid.Row="2" ItemsSource="{Binding ProcessSteps}" AutoGenerateColumns="False" 
                  CanUserAddRows="False" CanUserDeleteRows="False" 
                  GridLinesVisibility="All" HeadersVisibility="All"
                  AlternatingRowBackground="#F8F9FA" RowHeight="35">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60" IsReadOnly="True"/>
                            <DataGridTextColumn Header="任务编号" Binding="{Binding TaskCode}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="任务名称" Binding="{Binding TaskName}" Width="120" IsReadOnly="True"/>
                            <DataGridTextColumn Header="站点名称" Binding="{Binding SiteName}" Width="120" IsReadOnly="True"/>
                            <DataGridTextColumn Header="站点编号" Binding="{Binding SiteCode}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="计划数量" Binding="{Binding PlanQuantity}" Width="80" IsReadOnly="True"/>
                            <DataGridTextColumn Header="计划开工时间" Binding="{Binding PlanStartTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130" IsReadOnly="True"/>
                            <DataGridTextColumn Header="计划完工时间" Binding="{Binding PlanEndTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130" IsReadOnly="True"/>
                            <DataGridTextColumn Header="任务颜色" Binding="{Binding TaskColor}" Width="80" IsReadOnly="True"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            <TabItem Header="报工记录">
                <TextBlock Text="报工记录内容" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
            </TabItem>
            <TabItem Header="质检记录">
                <TextBlock Text="质检记录内容" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
            </TabItem>
            <TabItem Header="退料记录">
                <TextBlock Text="退料记录内容" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
            </TabItem>
            <TabItem Header="入库记录">
                <TextBlock Text="入库记录内容" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
            </TabItem>
            <TabItem Header="执行进度">
                <TextBlock Text="执行进度内容" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
            </TabItem>
        </TabControl>
        <Border Grid.RowSpan="4" Background="#80000000">
            <Border.Style>
                <Style TargetType="Border">
                    <Setter Property="Visibility" Value="Collapsed"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsLoading}" Value="True">
                            <Setter Property="Visibility" Value="Visible"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="加载中..." Foreground="White" FontSize="16" HorizontalAlignment="Center"/>
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,10,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
    

</UserControl>
