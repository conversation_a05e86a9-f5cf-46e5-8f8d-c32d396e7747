using System;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 工艺路线项目数据模型
    /// </summary>
    public class ProcessRouteItem : INotifyPropertyChanged
    {
        private string sequence;
        private int processScriptId;
        private string nextProcess;
        private string processRouteId;
        private string status;
        private string remarks;
        private string createUserId;
        private DateTime createTime;
        private string creatorUserName;
        private string updateUserName;
        private string id;

        [JsonPropertyName("sequence")]
        public string Sequence
        {
            get => sequence;
            set
            {
                sequence = value;
                OnPropertyChanged(nameof(Sequence));
            }
        }

        [JsonPropertyName("processScriptId")]
        public int ProcessScriptId
        {
            get => processScriptId;
            set
            {
                processScriptId = value;
                OnPropertyChanged(nameof(ProcessScriptId));
            }
        }

        [JsonPropertyName("nextProcess")]
        public string NextProcess
        {
            get => nextProcess;
            set
            {
                nextProcess = value;
                OnPropertyChanged(nameof(NextProcess));
            }
        }

        [JsonPropertyName("processRouteId")]
        public string ProcessRouteId
        {
            get => processRouteId;
            set
            {
                processRouteId = value;
                OnPropertyChanged(nameof(ProcessRouteId));
            }
        }

        [JsonPropertyName("status")]
        public string Status
        {
            get => status;
            set
            {
                status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        [JsonPropertyName("remarks")]
        public string Remarks
        {
            get => remarks;
            set
            {
                remarks = value;
                OnPropertyChanged(nameof(Remarks));
            }
        }

        [JsonPropertyName("createUserId")]
        public string CreateUserId
        {
            get => createUserId;
            set
            {
                createUserId = value;
                OnPropertyChanged(nameof(CreateUserId));
            }
        }

        [JsonPropertyName("createTime")]
        public DateTime CreateTime
        {
            get => createTime;
            set
            {
                createTime = value;
                OnPropertyChanged(nameof(CreateTime));
            }
        }

        [JsonPropertyName("creatorUserName")]
        public string CreatorUserName
        {
            get => creatorUserName;
            set
            {
                creatorUserName = value;
                OnPropertyChanged(nameof(CreatorUserName));
            }
        }

        [JsonPropertyName("updateUserName")]
        public string UpdateUserName
        {
            get => updateUserName;
            set
            {
                updateUserName = value;
                OnPropertyChanged(nameof(UpdateUserName));
            }
        }

        [JsonPropertyName("id")]
        public string Id
        {
            get => id;
            set
            {
                id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
