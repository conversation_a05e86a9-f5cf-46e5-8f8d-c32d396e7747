{"format": 1, "restore": {"D:\\实训二\\lkdsf]\\WPF\\WpfApp\\WpfApp.csproj": {}}, "projects": {"D:\\实训二\\lkdsf]\\WPF\\WpfApp\\WpfApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训二\\lkdsf]\\WPF\\WpfApp\\WpfApp.csproj", "projectName": "WpfApp", "projectPath": "D:\\实训二\\lkdsf]\\WPF\\WpfApp\\WpfApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训二\\lkdsf]\\WPF\\WpfApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\DevExpres\\Components\\Offline Packages", "D:\\VisualStudioSetup 2\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 21.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\DevExpres\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "MaterialDesignThemes.MahApps": {"target": "Package", "version": "[5.2.1, )"}, "Newtonsoft.Json.Bson": {"target": "Package", "version": "[1.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}