using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace WpfApp.Bom
{
    /// <summary>
    /// 工序API响应模型
    /// </summary>
    public class ProcessStepApiResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public ProcessStepResult Result { get; set; }
    }

    /// <summary>
    /// 工序结果数据模型
    /// </summary>
    public class ProcessStepResult
    {
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int Total { get; set; }
        public int TotalPages { get; set; }
        public ProcessStepData[] Items { get; set; }
    }

    /// <summary>
    /// 工序数据模型（根据实际API响应调整）
    /// </summary>
    public class ProcessStepData
    {
        public int Id { get; set; }  // 修改为int类型，因为API返回的是数字
        public string SerialCode { get; set; }
        public int ProcessStepId { get; set; }  // 修改为int类型，因为API返回的是数字
        public string ProcessName { get; set; }
        public int NextProcedure { get; set; }  // 修改为int类型，因为API返回的是数字
        public bool IsSystemCode { get; set; }
        public bool HasNextStep { get; set; }

        // 为了兼容性，保留原有字段
        public string ProcessStepName => ProcessName;
        public string ProcessStepCode => SerialCode;
        public string Description => "";
        public int StepOrder
        {
            get
            {
                // 尝试从SerialCode中解析序号
                if (int.TryParse(SerialCode, out int order))
                    return order;
                return 1;
            }
        }
        public int ProcessRouteId { get; set; }  // 修改为int类型，因为API返回的是数字
        public string? CreateTime { get; set; }
        public string? UpdateTime { get; set; }
        public int? CreatedUserId { get; set; }  // 修改为可空int类型，因为API返回的是数字
        public string? CreatedUserName { get; set; }
        public int? UpdatedUserId { get; set; }  // 修改为可空int类型，因为API返回的是数字
        public string? UpdatedUserName { get; set; }
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 工序项目模型（用于UI显示）
    /// </summary>
    public class ProcessStepItem : INotifyPropertyChanged
    {
        private bool _isSelected;
        private ObservableCollection<MaterialItem> _materials;

        public string Id { get; set; }
        public string ProcessStepName { get; set; }
        public string ProcessStepCode { get; set; }
        public string Description { get; set; }
        public int StepOrder { get; set; }
        public string ProcessRouteId { get; set; }
        public bool IsSystemCode { get; set; }
        public string CreateTime { get; set; }
        public string UpdateTime { get; set; }
        public string CreatedUserId { get; set; }
        public string CreatedUserName { get; set; }
        public string UpdatedUserId { get; set; }
        public string UpdatedUserName { get; set; }
        public string Remarks { get; set; }

        /// <summary>
        /// 是否被选中（用于标签页切换）
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 该工序的物料列表
        /// </summary>
        public ObservableCollection<MaterialItem> Materials
        {
            get => _materials ?? (_materials = new ObservableCollection<MaterialItem>());
            set
            {
                _materials = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 显示名称（用于标签页标题）
        /// </summary>
        public string DisplayName => $"工序{StepOrder}";

        /// <summary>
        /// 标签页标题（包含工序名称）
        /// </summary>
        public string TabTitle => $"{StepOrder}  {ProcessStepName}";

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
