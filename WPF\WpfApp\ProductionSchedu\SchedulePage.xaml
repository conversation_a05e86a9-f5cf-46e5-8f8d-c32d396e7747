﻿<UserControl x:Class="WpfApp.ProductionSchedu.SchedulePage"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp.ProductionSchedu"
        mc:Ignorable="d"
        >
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Grid Margin="20" Background="White" MinHeight="800">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="250"/>
                <RowDefinition Height="300"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="生产排产" FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Foreground="#007BFF" Margin="0,0,0,20"/>

        <!-- 基础信息区域 -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="20" CornerRadius="5" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="50"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="35"/>
                    <RowDefinition Height="35"/>
                    <RowDefinition Height="35"/>
                    <RowDefinition Height="35"/>
                    <RowDefinition Height="35"/>
                    <RowDefinition Height="35"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="工单编号:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding WorkOrderCode}" VerticalAlignment="Center" FontWeight="Bold"/>

                <TextBlock Grid.Row="0" Grid.Column="3" Text="工单名称:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="0" Grid.Column="4" Text="{Binding WorkOrderName}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="关联计划:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding PlanName}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="1" Grid.Column="3" Text="计划编号:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="1" Grid.Column="4" Text="{Binding PlanCode}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="类型:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding ProductType}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="2" Grid.Column="3" Text="产品名称:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="2" Grid.Column="4" Text="{Binding ProductName}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="产品编号:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding ProductCode}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="3" Grid.Column="3" Text="规格型号:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="3" Grid.Column="4" Text="{Binding Specification}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="4" Grid.Column="0" Text="产品类型:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding ProductCategory}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="4" Grid.Column="3" Text="单位:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="4" Grid.Column="4" Text="{Binding Unit}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="5" Grid.Column="0" Text="BOM:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="5" Grid.Column="1" Text="{Binding BomCode}" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="5" Grid.Column="3" Text="版本:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="5" Grid.Column="4" Text="{Binding Version}" VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- 排产表单 -->
        <Border Grid.Row="2" Background="White" BorderBrush="#DEE2E6" BorderThickness="1" Padding="20" CornerRadius="5" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="50"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="80"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="计划数量" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding PlanNumber}" Height="30"/>

                <TextBlock Grid.Row="0" Grid.Column="3" Text="计划开工时间" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <DatePicker Grid.Row="0" Grid.Column="4" SelectedDate="{Binding PlanStartTime}" Height="30"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="计划完工时间" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <DatePicker Grid.Row="1" Grid.Column="1" SelectedDate="{Binding PlanEndTime}" Height="30"/>

                <TextBlock Grid.Row="1" Grid.Column="3" Text="需求日期" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <DatePicker Grid.Row="1" Grid.Column="4" SelectedDate="{Binding DemandTime}" Height="30"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="备注" VerticalAlignment="Top" Margin="0,10,10,0"/>
                <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="4" Text="{Binding Remarks}" 
                         TextWrapping="Wrap" AcceptsReturn="True" Height="60" Margin="0,5,0,0"/>
            </Grid>
        </Border>

        <!-- 物料清单 -->
        <Border Grid.Row="3" Background="White" BorderBrush="#DEE2E6" BorderThickness="1" Padding="20" CornerRadius="5" Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="物料清单" FontSize="16" FontWeight="Bold" 
                           Foreground="#FFC107" Margin="0,0,0,10"/>

                <DataGrid Grid.Row="1" ItemsSource="{Binding Materials}" AutoGenerateColumns="False" 
                          CanUserAddRows="False" CanUserDeleteRows="False" 
                          GridLinesVisibility="All" HeadersVisibility="All"
                          AlternatingRowBackground="#F8F9FA" RowHeight="35">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60" IsReadOnly="True"/>
                        <DataGridTextColumn Header="物料编号" Binding="{Binding MaterialCode}" Width="120" IsReadOnly="True"/>
                        <DataGridTextColumn Header="物料名称" Binding="{Binding MaterialName}" Width="150" IsReadOnly="True"/>
                        <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" Width="120" IsReadOnly="True"/>
                        <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="80" IsReadOnly="True"/>
                        <DataGridTextColumn Header="需求使用量" Binding="{Binding RequiredQuantity}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="用料比例" Binding="{Binding UsageRatio}" Width="100" IsReadOnly="True"/>
                    </DataGrid.Columns>
                </DataGrid>

                <TextBlock Grid.Row="2" HorizontalAlignment="Right" Margin="0,10,0,0">
                    <Run Text="物料总量: "/>
                    <Run Text="{Binding TotalMaterialQuantity}" FontWeight="Bold"/>
                </TextBlock>
            </Grid>
        </Border>

        <!-- 工艺路线 -->
        <Border Grid.Row="4" Background="White" BorderBrush="#DEE2E6" BorderThickness="1" Padding="20" CornerRadius="5" Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="工艺路线" FontSize="16" FontWeight="Bold" 
                           Foreground="#FFC107" Margin="0,0,0,10"/>

                    <Grid Grid.Row="1" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="50"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="50"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="50"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="工艺路线名称:" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" Text="{Binding ProcessRouteName}" VerticalAlignment="Center" FontWeight="Bold"/>

                        <TextBlock Grid.Column="3" Text="工艺路线编号:" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="4" Text="{Binding ProcessRouteCode}" VerticalAlignment="Center"/>

                        <TextBlock Grid.Column="6" Text="选择工序:" VerticalAlignment="Center"/>
                        <ComboBox Grid.Column="7" ItemsSource="{Binding ProcessStepOptions}" 
                            SelectedItem="{Binding SelectedProcessStepId}" Height="30"/>

                        <Button Grid.Column="9" Content="新增" Background="#007BFF" Foreground="White" 
                        Padding="15,5" BorderThickness="0" FontSize="12" Height="30"
                        Command="{Binding AddTaskCommand}"/>
                    </Grid>

                    <DataGrid Grid.Row="2" ItemsSource="{Binding ProcessSteps}" AutoGenerateColumns="False" 
                          CanUserAddRows="False" CanUserDeleteRows="False" 
                          GridLinesVisibility="All" HeadersVisibility="All"
                          AlternatingRowBackground="#F8F9FA" RowHeight="35">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60" IsReadOnly="True"/>
                        <DataGridTextColumn Header="任务编号" Binding="{Binding TaskCode}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="任务名称" Binding="{Binding TaskName}" Width="120" IsReadOnly="True"/>
                        <DataGridTextColumn Header="站点名称" Binding="{Binding SiteName}" Width="120" IsReadOnly="True"/>
                        <DataGridTextColumn Header="站点编号" Binding="{Binding SiteCode}" Width="100" IsReadOnly="True"/>
                        <DataGridTextColumn Header="计划数量" Binding="{Binding PlanQuantity}" Width="80" IsReadOnly="True"/>
                        <DataGridTextColumn Header="计划开工时间" Binding="{Binding PlanStartTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130" IsReadOnly="True"/>
                        <DataGridTextColumn Header="计划完工时间" Binding="{Binding PlanEndTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130" IsReadOnly="True"/>
                        <DataGridTextColumn Header="任务颜色" Binding="{Binding TaskColor}" Width="80" IsReadOnly="True"/>
                            <DataGridTemplateColumn Header="操作" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="编辑" Margin="2" Padding="6,3" FontSize="11" 
                            Background="#007BFF" Foreground="White" BorderThickness="0"
                            Command="{Binding DataContext.EditTaskCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                            CommandParameter="{Binding}"/>
                                            <Button Content="删除" Margin="2" Padding="6,3" FontSize="11" 
                            Background="#DC3545" Foreground="White" BorderThickness="0"
                            Command="{Binding DataContext.DeleteTaskCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                            CommandParameter="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- 按钮 -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="提交排产" Command="{Binding SubmitCommand}" Background="#007BFF" Foreground="White" 
                    Padding="20,10" Margin="0,0,15,0" BorderThickness="0" FontSize="14"/>
            <Button Content="返回" Command="{Binding BackCommand}" Background="#6C757D" Foreground="White" 
                    Padding="20,10" BorderThickness="0" FontSize="14" Click="Button_Click"/>
        </StackPanel>
    </Grid>
    </ScrollViewer>
</UserControl>
