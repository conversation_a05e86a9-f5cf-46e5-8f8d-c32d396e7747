using System.Windows;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 添加工序窗口12
    /// </summary>
    public partial class AddProcessStepWindow : Window
    {
        public AddProcessStepWindow()
        {
            InitializeComponent();
            DataContext = new AddProcessStepViewModel();
        }

        /// <summary>
        /// 带回调的构造函数
        /// </summary>
        /// <param name="onProcessAdded">工序添加成功后的回调</param>
        public AddProcessStepWindow(System.Action<ProcessStepItem> onProcessAdded)
        {
            InitializeComponent();
            var viewModel = new AddProcessStepViewModel();
            viewModel.ProcessAdded += onProcessAdded;
            DataContext = viewModel;
        }

        /// <summary>
        /// 带工艺路线ID和回调的构造函数
        /// </summary>
        /// <param name="processRouteId">工艺路线ID</param>
        /// <param name="onProcessAdded">工序添加成功后的回调</param>
        public AddProcessStepWindow(string processRouteId, System.Action<ProcessStepItem> onProcessAdded)
        {
            InitializeComponent();
            var viewModel = new AddProcessStepViewModel(processRouteId);
            viewModel.ProcessAdded += onProcessAdded;
            DataContext = viewModel;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {

        }

        private void ComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {

        }
    }
}
