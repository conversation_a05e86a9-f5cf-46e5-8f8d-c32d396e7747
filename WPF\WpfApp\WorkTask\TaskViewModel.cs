﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows;
using System.Runtime.CompilerServices;

namespace WpfApp.WorkTask
{
    public class TaskViewModel : INotifyPropertyChanged
    {
        private int _page = 1;
        private int _pageSize = 10;
        private int _total;
        private string _taskCode = "";
        private string _workOrderCode = "";
        private long? _processStepId = null;
        private int? _status = null;
        private ObservableCollection<TaskModel> _tasks;

        public int Page
        {
            get => _page;
            set
            {
                if (_page != value)
                {
                    _page = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Page)));
                    LoadData();
                }
            }
        }

        public int PageSize
        {
            get => _pageSize;
            set
            {
                if (_pageSize != value)
                {
                    _pageSize = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(PageSize)));
                    LoadData();
                }
            }
        }

        public int Total
        {
            get => _total;
            set
            {
                if (_total != value)
                {
                    _total = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Total)));
                }
            }
        }

        public int TotalPages
        {
            get => _pageSize;
            set
            {
                if (_pageSize != value)
                {
                    _pageSize = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(TotalPages)));
                }
            }
        }

        public string TaskCode
        {
            get => _taskCode;
            set { _taskCode = value; OnPropertyChanged(nameof(TaskCode)); }
        }

        public string WorkOrderCode
        {
            get => _workOrderCode;
            set { _workOrderCode = value; OnPropertyChanged(nameof(WorkOrderCode)); }
        }

        public long? ProcessStepId
        {
            get => _processStepId;
            set { _processStepId = value; OnPropertyChanged(nameof(ProcessStepId)); }
        }

        public int? Status
        {
            get => _status;
            set { _status = value; OnPropertyChanged(nameof(Status)); }
        }


        public ObservableCollection<TaskModel> Tasks
        {
            get => _tasks;
            set
            {
                if (_tasks != value)
                {
                    _tasks = value;
                    OnPropertyChanged(nameof(Tasks));
                }
            }
        }

        public ICommand FirstPageCommand {  get; set; }
        public ICommand PrevPageCommand { get; set; }
        public ICommand NextPageCommand { get; set; }
        public ICommand LastPageCommand {  get; set; }
        public ICommand SearchCommand { get; set; }
        public ICommand ResetCommand {  get; set; }
        public ICommand DispatchCommand { get; set; }
        public ICommand StartCommand {  get; set; }
        public ICommand CloseCommand { get; set; }

        public TaskViewModel()
        {
            Tasks = new ObservableCollection<TaskModel>();
            
            SearchCommand = new RelayCommand(_ => LoadData());
            ResetCommand = new RelayCommand(_ => Reset());
            FirstPageCommand = new RelayCommand(_ => { Page = 1; LoadData(); });
            PrevPageCommand = new RelayCommand(_ => { if (Page > 1) { Page--; LoadData(); } });
            NextPageCommand = new RelayCommand(_ => { if (Page < TotalPages) { Page++; LoadData(); } });
            LastPageCommand = new RelayCommand(_ => { Page = TotalPages; LoadData(); });
            DispatchCommand = new RelayCommand(DispatchTask);
            StartCommand = new RelayCommand(StartTask);
            CloseCommand = new RelayCommand(CloseTask);

            System.Diagnostics.Debug.WriteLine("TaskViewModel 构造函数完成，开始加载数据...");
            
            // 简化初始化调用
            LoadData();
        }
        private void Reset()
        {
            TaskCode = "";
            WorkOrderCode = "";
            ProcessStepId = null;
            Status = null;
            Page = 1;
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始加载任务数据...");

                using var client = new HttpClient();

                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var queryParams = new List<string>
                {
                    $"Index={Page}",
                    $"Size={PageSize}"
                };

                if (!string.IsNullOrEmpty(TaskCode))
                    queryParams.Add($"TaskCode={Uri.EscapeDataString(TaskCode)}");
                if (!string.IsNullOrEmpty(WorkOrderCode))
                    queryParams.Add($"WorkOrderCode={Uri.EscapeDataString(WorkOrderCode)}");
                if (ProcessStepId.HasValue)
                    queryParams.Add($"ProcessStepId={ProcessStepId.Value}");
                if (Status.HasValue)
                    queryParams.Add($"Status={Status.Value}");

                var url = $"http://localhost:5005/api/task/workOrderTask?{string.Join("&", queryParams)}";
                System.Diagnostics.Debug.WriteLine($"请求URL: {url}");

                var response = await client.GetAsync(url);
                System.Diagnostics.Debug.WriteLine($"HTTP状态码: {response.StatusCode}");

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"HTTP错误: {errorContent}");
                    MessageBox.Show($"请求失败: {response.StatusCode}", "错误");
                    return;
                }

                var json = await response.Content.ReadAsStringAsync();
                System.Diagnostics.Debug.WriteLine($"API返回数据: {json}");

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var result = JsonSerializer.Deserialize<TaskPageResponse>(json, options);
                System.Diagnostics.Debug.WriteLine($"反序列化结果: Code={result?.Code}, Message={result?.Message}");

                if (result?.Code == 200 && result.Result != null)
                {
                    var items = result.Result.Items ?? new List<TaskApiModel>();
                    System.Diagnostics.Debug.WriteLine($"获取到 {items.Count} 条数据");

                    var tasks = new List<TaskModel>();
                    for (int i = 0; i < items.Count; i++)
                    {
                        var item = items[i];
                        tasks.Add(new TaskModel
                        {
                            Index = (Page - 1) * PageSize + i + 1,
                            TaskId = item.TaskId ?? "",
                            TaskNumber = item.TaskNumber ?? "",
                            TaskName = item.TaskName ?? "",
                            SiteName = item.SiteName ?? "",
                            WorkOrderCode = item.WorkOrderCode ?? "",
                            WorkOrderName = item.WorkOrderName ?? "",
                            SiteCode = item.SiteCode ?? "",
                            ProcessRouteName = item.ProcessRouteName ?? "",
                            ProcessName = item.ProcessName ?? "",
                            ProcessCode = item.ProcessCode ?? "",
                            TaskColor = item.TaskColor ?? "",
                            PlanQuantity = item.PlanQuantity,
                            RealityQuantity = item.RealityQuantity,
                            StartTime = item.StartTime,
                            EndTime = item.EndTime,
                            RealityStartTime = item.RealityStartTime,
                            RealityEndTime = item.RealityEndTime,
                            TaskStatus = item.TaskStatus,
                            StatusText = GetStatusText(item.TaskStatus)
                        });
                    }

                    Tasks.Clear();
                    foreach (var task in tasks)
                    {
                        Tasks.Add(task);
                    }

                    Total = result.Result.Total;
                    OnPropertyChanged(nameof(TotalPages));

                    System.Diagnostics.Debug.WriteLine($"数据加载完成: {Tasks.Count} 条记录，总计 {Total} 条");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"API返回错误或无数据: Code={result?.Code}");
                    Tasks.Clear();
                    Total = 0;
                    OnPropertyChanged(nameof(TotalPages));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载数据异常: {ex.Message}");
                MessageBox.Show($"加载数据失败：{ex.Message}", "错误");
            }
        }

        private string GetStatusText(int status)
        {
            return status switch
            {
                1 => "已下达",
                2 => "进行中",
                3 => "已完成",
                _ => "未派工"
            };
        }

        private void DispatchTask(object parameter)
        {
            if (parameter is TaskModel task)
            {
                if (string.IsNullOrEmpty(task.TaskId))
                {
                    MessageBox.Show("任务ID为空，无法派工", "错误");
                    return;
                }

                var dialog = new Window
                {
                    Title = "任务派工",
                    Width = 800,
                    Height = 700,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    ResizeMode = ResizeMode.NoResize,
                    MinWidth=700,
                    MinHeight=600,
                };

                var viewModel = new DispatchTaskViewModel(task.TaskId);
                var page = new DispatchTaskPage { DataContext = viewModel };
                dialog.Content = page;

                viewModel.DialogClosed += () =>
                {
                    dialog.Close();
                    LoadData(); // 刷新数据
                };

                dialog.ShowDialog();
            }
        }

        private void StartTask(object parameter)
        {
            if (parameter is TaskModel task)
            {
                // 实现开工逻辑
                MessageBox.Show($"开工任务：{task.TaskName}", "开工");
            }
        }

        private void CloseTask(object parameter)
        {
            if (parameter is TaskModel task)
            {
                // 实现关闭逻辑
                MessageBox.Show($"关闭任务：{task.TaskName}", "关闭");
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName]string propertyName=null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
    // API响应模型
    public class TaskPageResponse
    {
        public int Code { get; set; }
        public string Message { get; set; }
        public TaskPageResult Result { get; set; }
    }

    public class TaskPageResult
    {
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int Total { get; set; }
        public int TotalPages { get; set; }
        public List<TaskApiModel> Items { get; set; }
    }
    public class TaskApiModel
    {
        public string TaskId {  get; set; }
        public string TaskNumber { get; set; }
        public string TaskName { get; set; }
        public string SiteName { get; set; }
        public string WorkOrderCode { get; set; }
        public string WorkOrderName { get; set; }
        public string SiteCode { get; set; }
        public string ProcessRouteName { get; set; }
        public string ProcessName { get; set; }
        public string ProcessCode { get; set; }
        public string TaskColor { get; set; }
        public int PlanQuantity { get; set; }
        public int RealityQuantity { get; set; }
        public string StartTime { get; set; }
        public string EndTime { get; set; }
        public string RealityStartTime { get; set; }
        public string RealityEndTime { get; set; }
        public int TaskStatus { get; set; }
    }
}
