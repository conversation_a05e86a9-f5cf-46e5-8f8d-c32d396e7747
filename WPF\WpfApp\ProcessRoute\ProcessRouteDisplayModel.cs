using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text.Json.Serialization;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 工艺路线显示数据模型类
    /// </summary>
    public class ProcessRouteDisplayModel : INotifyPropertyChanged
    {
        private bool _isSelected;

        /// <summary>
        /// 是否被选中 - 用于复选框绑定
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        /// <summary>
        /// 工艺路线编码
        /// </summary>
        [JsonPropertyName("processRouteCode")]
        public string ProcessRouteCode { get; set; }

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        [JsonPropertyName("processRouteName")]
        public string ProcessRouteName { get; set; }

        /// <summary>
        /// 状态 - 布尔值，true表示启用，false表示禁用
        /// </summary>
        [JsonPropertyName("status")]
        public bool Status { get; set; }

        /// <summary>
        /// 状态显示文本 - 只读属性，将布尔值转换为中文显示
        /// </summary>
        public string StatusDisplay => Status ? "启用" : "禁用";

        /// <summary>
        /// 备注信息
        /// </summary>
        [JsonPropertyName("remarks")]
        public string Remarks { get; set; }
        public string Id { get; set; }

        /// <summary>
        /// 序号 - 用于界面显示，从1开始递增
        /// </summary>
        public int RowNumber { get; set; }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
