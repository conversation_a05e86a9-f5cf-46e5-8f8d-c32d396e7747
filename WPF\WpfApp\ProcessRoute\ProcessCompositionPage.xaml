<!-- 工艺路线组成页面用户控件定义 -->
<UserControl x:Class="WpfApp.ProcessRoute.ProcessCompositionPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <!-- 主网格容器，设置外边距为10像素 -->
    <Grid Margin="10">
        <!-- 定义网格行布局 -->
        <Grid.RowDefinitions>
            <!-- 第一行：标题栏，自动高度 -->
            <RowDefinition Height="Auto"/>
            <!-- 第二行：查询条件区域，自动高度 -->
            <RowDefinition Height="Auto"/>
            <!-- 第三行：数据表格区域，占用剩余空间 -->
            <RowDefinition Height="*"/>
            <!-- 第四行：分页控件区域，自动高度 -->
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏区域 -->
        <TextBlock Grid.Row="0" Text="工艺路线管理" FontSize="24" FontWeight="Bold"
                   Margin="0,0,0,20" HorizontalAlignment="Center"/>

        <!-- 查询条件区域 -->
        <!-- 使用Material Design卡片样式包装查询条件 -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="15">
            <!-- 水平排列的查询控件容器 -->
            <StackPanel Orientation="Horizontal">
                <!-- 工艺路线ID标签 -->
                <TextBlock Text="工艺路线ID:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <!-- 工艺路线ID输入框 -->
                <TextBox x:Name="ProcessRouteIdTextBox" Width="200" Margin="0,0,20,0"
                         materialDesign:HintAssist.Hint="请输入工艺路线ID"
                         Text="{Binding ProcessRouteIdFilter, UpdateSourceTrigger=PropertyChanged}"/>
                <!-- 查询按钮 -->
                <Button Content="查询" Command="{Binding SearchCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Background="#673AB7" Foreground="White" Margin="0,0,10,0"/>
                <!-- 清空按钮 -->
                <Button Content="清空" Command="{Binding ClearCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,10,0"/>
                <!-- 刷新按钮 -->
                <Button Content="刷新" Command="{Binding RefreshCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,10,0"/>
                <!-- 新增按钮 -->
                <Button Content="新增工艺路线" Command="{Binding AddCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Background="#4CAF50" Foreground="White"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 数据表格区域 -->
        <!-- 使用Material Design卡片样式包装数据表格 -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <!-- 数据表格控件 -->
            <DataGrid ItemsSource="{Binding ProcessCompositions}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionMode="Single"
                      AlternatingRowBackground="#F5F5F5" SelectionChanged="DataGrid_SelectionChanged">
                <!-- 定义数据表格列 -->
                <DataGrid.Columns>
                    <!-- 序号列 -->
                    <DataGridTextColumn Header="序号" Width="60" Binding="{Binding SerialCode}"/>
                    <!-- 工序脚本ID列 -->
                    <DataGridTextColumn Header="工序脚本ID" Width="120" Binding="{Binding ProcessScriptId}"/>
                    <!-- 下一工序列 -->
                    <DataGridTextColumn Header="下一工序" Width="120" Binding="{Binding NextProcedure}"/>
                    <!-- 工艺路线ID列 -->
                    <DataGridTextColumn Header="工艺路线ID" Width="150" Binding="{Binding ProcessRouteId}"/>
                    <!-- 状态列 -->
                    <DataGridTextColumn Header="状态" Width="80" Binding="{Binding StatusDisplay}"/>
                    <!-- 备注列 -->
                    <DataGridTextColumn Header="备注" Width="150" Binding="{Binding Remarks}"/>
                    <!-- 创建时间列 -->
                    <DataGridTextColumn Header="创建时间" Width="140" Binding="{Binding CreateTimeDisplay}"/>
                    <!-- 操作列 - 使用模板列定义操作按钮 -->
                    <DataGridTemplateColumn Header="操作" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <!-- 水平排列的操作按钮容器 -->
                                <StackPanel Orientation="Horizontal">
                                    <!-- 编辑按钮 -->
                                    <Button Content="编辑"
                                            Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            Foreground="#2196F3" FontSize="12" Padding="5,2"/>
                                    <!-- 删除按钮 -->
                                    <Button Content="删除"
                                            Command="{Binding DataContext.DeleteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            Foreground="#F44336" FontSize="12" Padding="5,2"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>

        <!-- 分页控件区域 -->
        <!-- 水平居中排列的分页控件容器 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center"
                    VerticalAlignment="Center" Margin="0,10,0,0">
            <!-- 上一页按钮 -->
            <Button Content="上一页" Command="{Binding PrevPageCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}" Margin="5"/>
            <!-- 当前页信息显示 -->
            <TextBlock Text="{Binding CurrentPageDisplay}" VerticalAlignment="Center" Margin="10,0"/>
            <!-- 下一页按钮 -->
            <Button Content="下一页" Command="{Binding NextPageCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}" Margin="5"/>
            <!-- 总数标签 -->
            <TextBlock Text="总数:" VerticalAlignment="Center" Margin="20,0,5,0"/>
            <!-- 总数值显示 -->
            <TextBlock Text="{Binding TotalCount}" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <!-- 每页标签 -->
            <TextBlock Text="每页:" VerticalAlignment="Center" Margin="10,0,5,0"/>
            <!-- 每页数量选择下拉框 -->
            <ComboBox SelectedValue="{Binding PageSize}" Width="60" Margin="0,0,10,0">
                <!-- 每页10条选项 -->
                <ComboBoxItem Content="10"/>
                <!-- 每页20条选项 -->
                <ComboBoxItem Content="20"/>
                <!-- 每页50条选项 -->
                <ComboBoxItem Content="50"/>
                <!-- 每页100条选项 -->
                <ComboBoxItem Content="100"/>
            </ComboBox>
        </StackPanel>
    </Grid>
</UserControl>
