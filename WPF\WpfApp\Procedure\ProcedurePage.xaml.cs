using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;

namespace WpfApp.Procedure
{
    /// <summary>
    /// 工序管理页面的交互逻辑类
    /// </summary>
    public partial class ProcedurePage : UserControl
    {
        private ProcedureViewModel _viewModel;

        /// <summary>
        /// 构造函数 - 初始化工序管理页面
        /// </summary>
        public ProcedurePage()
        {
            // 初始化XAML组件
            InitializeComponent();

            // 设置数据上下文为视图模型实例
            _viewModel = new ProcedureViewModel();
            DataContext = _viewModel;

            // 订阅ViewModel属性变化事件
            _viewModel.PropertyChanged += ViewModel_PropertyChanged;
        }

        /// <summary>
        /// ViewModel属性变化事件处理
        /// </summary>
        private void ViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ProcedureViewModel.IsAllSelected))
            {
                var selectAllCheckBox = FindName("selectAllCheckBox") as CheckBox;
                if (selectAllCheckBox != null && DataContext is ProcedureViewModel viewModel)
                {
                    selectAllCheckBox.IsChecked = viewModel.IsAllSelected;
                }
            }
        }

        /// <summary>
        /// 全选复选框选中事件
        /// </summary>
        private void SelectAllCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (DataContext is ProcedureViewModel viewModel)
            {
                viewModel.IsAllSelected = true;
            }
        }

        /// <summary>
        /// 全选复选框取消选中事件
        /// </summary>
        private void SelectAllCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            if (DataContext is ProcedureViewModel viewModel)
            {
                viewModel.IsAllSelected = false;
            }
        }
    }
}