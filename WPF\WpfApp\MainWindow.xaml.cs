﻿using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;

namespace WpfApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            // 初始显示登录卡片
            MainContent.Content = new LoginCard(this);
        }

        // 登录成功后调用123123123
        // public void ShowBomPage()
        // {
        //     var bomPage = new BomPage();
        //     bomPage.DataContext = new BomViewModel();
        //     MainContent.Content = bomPage;
        // }

        public void ShowMenuPage()
        {
            MainContent.Content = new MenuPage();
        }
    }
}