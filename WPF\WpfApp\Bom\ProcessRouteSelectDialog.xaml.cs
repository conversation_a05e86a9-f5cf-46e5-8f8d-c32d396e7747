using System;
using System.Windows;

namespace WpfApp.Bom
{
    /// <summary>
    /// ProcessRouteSelectDialog.xaml 的交互逻辑
    /// </summary>
    public partial class ProcessRouteSelectDialog : Window
    {
        public ProcessRouteItem SelectedProcessRoute { get; private set; }
        private ProcessRouteSelectViewModel _viewModel;

        public ProcessRouteSelectDialog()
        {
            InitializeComponent();

            _viewModel = new ProcessRouteSelectViewModel();
            DataContext = _viewModel;

            // 订阅事件
            _viewModel.ProcessRouteSelected += OnProcessRouteSelected;
            _viewModel.CloseRequested += OnCloseRequested;
        }

        private void OnProcessRouteSelected(ProcessRouteItem processRoute)
        {
            SelectedProcessRoute = processRoute;

            try
            {
                // 只有在对话框正确显示时才能设置DialogResult
                if (this.IsLoaded)
                {
                    DialogResult = true;
                }
                else
                {
                    // 如果对话框还没有完全加载，直接关闭
                    this.Close();
                }
            }
            catch (InvalidOperationException)
            {
                // 如果设置DialogResult失败，直接关闭窗口
                this.Close();
            }
        }

        private void OnCloseRequested()
        {
            try
            {
                // 只有在对话框正确显示时才能设置DialogResult
                if (this.IsLoaded)
                {
                    DialogResult = false;
                }
                else
                {
                    // 如果对话框还没有完全加载，直接关闭
                    this.Close();
                }
            }
            catch (InvalidOperationException)
            {
                // 如果设置DialogResult失败，直接关闭窗口
                this.Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            // 释放资源
            _viewModel?.Dispose();
            base.OnClosed(e);
        }
    }
}
