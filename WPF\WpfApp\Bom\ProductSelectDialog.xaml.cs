using System.Windows;

namespace WpfApp
{
    public partial class ProductSelectDialog : Window
    {
        public ProductSelectDialog()
        {
            InitializeComponent();
            DataContext = new ProductSelectViewModel();
            
            // 订阅ViewModel的关闭事件
            if (DataContext is ProductSelectViewModel viewModel)
            {
                viewModel.CloseRequested += (result) =>
                {
                    DialogResult = result;
                    Close();
                };
            }
        }

        public ProductSelectDialog(ProductSelectViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // 订阅ViewModel的关闭事件
            viewModel.CloseRequested += (result) =>
            {
                DialogResult = result;
                Close();
            };
        }
    }
}
