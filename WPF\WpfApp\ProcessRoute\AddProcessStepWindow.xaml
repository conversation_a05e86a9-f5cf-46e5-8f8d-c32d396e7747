<Window x:Class="WpfApp.ProcessRoute.AddProcessStepWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="添加工序" Height="600" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="添加工序组成" FontSize="20" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- 表单内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- 序号 -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="序号" FontWeight="Bold" Margin="0,0,0,5">
                        <Run Text="*" Foreground="Red"/>
                    </TextBlock>
                    <TextBox materialDesign:HintAssist.Hint="请输入序号"
                             Text="{Binding Sequence, UpdateSourceTrigger=PropertyChanged}"/>
                </StackPanel>

                <!-- 工序选择 -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="工序" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox materialDesign:HintAssist.Hint="请选择工序"
                              ItemsSource="{Binding AvailableProcesses}"
                              SelectedItem="{Binding SelectedProcess, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                              DisplayMemberPath="ProcessName"/>
                </StackPanel>

                <!-- 下一道工序 -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="下一道工序" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox materialDesign:HintAssist.Hint="请选择下一道工序"
                              ItemsSource="{Binding AvailableProcesses}"
                              SelectedItem="{Binding SelectedNextProcess, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                              DisplayMemberPath="ProcessName"/>
                </StackPanel>

                <!-- 关系 -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="关系" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox materialDesign:HintAssist.Hint="请选择与下一道工序关系"
                              ItemsSource="{Binding AvailableRelationships}"
                              SelectedItem="{Binding SelectedRelationship}" SelectionChanged="ComboBox_SelectionChanged"/>
                </StackPanel>

                <!-- 关键工序 -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="关键工序" FontWeight="Bold" Margin="0,0,0,5"/>
                    <StackPanel Orientation="Horizontal">
                        <RadioButton Content="是" IsChecked="{Binding IsKeyProcess}"
                                     GroupName="KeyProcess" Margin="0,0,20,0"/>
                        <RadioButton Content="否" IsChecked="{Binding IsNotKeyProcess}"
                                     GroupName="KeyProcess"/>
                    </StackPanel>
                </StackPanel>

                <!-- 显示颜色 -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="显示颜色" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox materialDesign:HintAssist.Hint="请输入颜色值 (如: #1B9AEE)"
                             Text="{Binding DisplayColor, UpdateSourceTrigger=PropertyChanged}"/>
                </StackPanel>



                <!-- 准备时间 -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="准备时间" FontWeight="Bold" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal">
                            <TextBox materialDesign:HintAssist.Hint="请输入"
                                     Text="{Binding PrepareTime, UpdateSourceTrigger=PropertyChanged}"
                                     Width="120"/>
                            <TextBlock Text="小时" VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="等待时间" FontWeight="Bold" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal">
                            <TextBox materialDesign:HintAssist.Hint="请输入"
                                     Text="{Binding WaitTime, UpdateSourceTrigger=PropertyChanged}"
                                     Width="120"/>
                            <TextBlock Text="小时" VerticalAlignment="Center" Margin="10,0,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>

                <!-- 备注 -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="备注" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox materialDesign:HintAssist.Hint="请输入"
                             Text="{Binding Remarks, UpdateSourceTrigger=PropertyChanged}"
                             Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                             VerticalScrollBarVisibility="Auto"/>
                </StackPanel>

            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal"
                    HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="取消" Command="{Binding CancelCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="80" Height="35" Margin="0,0,10,0"/>
            <Button Content="确定" Command="{Binding ConfirmCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Width="80" Height="35" Click="Button_Click"/>
        </StackPanel>
    </Grid>
</Window>
