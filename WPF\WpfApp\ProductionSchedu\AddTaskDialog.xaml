﻿<Window x:Class="WpfApp.ProductionSchedu.AddTaskDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp.ProductionSchedu"
        mc:Ignorable="d"
        Title="新增" Height="450" Width="800" WindowStartupLocation="CenterScreen" ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 第一行：任务编号和任务名称 -->
        <Grid Grid.Row="0" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="* 任务编号" VerticalAlignment="Center" Foreground="Red" Margin="0,0,10,0"/>
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <TextBox Text="{Binding TaskCode, UpdateSourceTrigger=PropertyChanged}" 
                 Width="150" Height="30" 
                 IsEnabled="{Binding IsManualTaskCode}"
                 />
                <ToggleButton IsChecked="{Binding IsSystemGenerated}" Width="60" Height="30" Margin="10,0,0,0">
                    <TextBlock Text="系统编号" FontSize="10"/>
                </ToggleButton>
            </StackPanel>

            <TextBlock Grid.Column="3" Text="* 任务名称" VerticalAlignment="Center" Foreground="Red" Margin="20,0,10,0"/>
            <TextBox Grid.Column="4" Text="{Binding TaskName, UpdateSourceTrigger=PropertyChanged}" 
             Height="30" />
        </Grid>

        <!-- 第二行：站点名称和站点编号 -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="* 站点名称" VerticalAlignment="Center" Foreground="Red" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="1" ItemsSource="{Binding SiteOptions}" 
                      SelectedItem="{Binding SelectedSite}" Height="30"
                      DisplayMemberPath="SiteName"/>
            <TextBlock Grid.Column="2" Text="站点编号" VerticalAlignment="Center" Margin="20,0,10,0"/>
            <TextBox Grid.Column="3" Text="{Binding SiteCode}" Height="30" IsReadOnly="True"/>
        </Grid>

        <!-- 第三行：任务颜色和计划数量 -->
        <Grid Grid.Row="2" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="* 任务颜色" VerticalAlignment="Center" Foreground="Red" Margin="0,0,10,0"/>
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Rectangle Width="30" Height="30" Fill="{Binding TaskColor}" Stroke="Gray" StrokeThickness="1"/>
                <TextBox Text="{Binding TaskColorHex}" Width="100" Height="30" Margin="10,0,0,0"/>
            </StackPanel>

            <TextBlock Grid.Column="2" Text="* 计划数量" VerticalAlignment="Center" Foreground="Red" Margin="20,0,10,0"/>
            <TextBox Grid.Column="3" Text="{Binding PlanQuantity}" Height="30"/>
        </Grid>

        <!-- 第四行：开工时间和完工时间 -->
        <Grid Grid.Row="3" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="* 开工时间" VerticalAlignment="Center" Foreground="Red" Margin="0,0,10,0"/>
            <DatePicker Grid.Column="1" SelectedDate="{Binding StartTime}" Height="30"/>

            <TextBlock Grid.Column="2" Text="* 完工时间" VerticalAlignment="Center" Foreground="Red" Margin="20,0,10,0"/>
            <DatePicker Grid.Column="3" SelectedDate="{Binding EndTime}" Height="30"/>
        </Grid>

        <!-- 按钮 -->
        <StackPanel Grid.Row="6" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="取消" Command="{Binding CancelCommand}" Background="#6C757D" Foreground="White" 
                    Padding="20,10" Margin="0,0,15,0" BorderThickness="0" FontSize="14"/>
            <Button Content="确定" Command="{Binding ConfirmCommand}" Background="#007BFF" Foreground="White" 
                    Padding="20,10" BorderThickness="0" FontSize="14"/>
        </StackPanel>
    </Grid>
</Window>
