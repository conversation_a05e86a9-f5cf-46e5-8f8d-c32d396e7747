using System.ComponentModel;
using System.Collections.Generic;
using System.Text.Json.Serialization;

public class BomProduct : INotifyPropertyChanged
{
    private bool _isSelected;

    /// <summary>
    /// BOM 编号
    /// </summary>
    public string BomCode { get; set; }

    /// <summary>
    /// 是否系统编号（开关状态）
    /// </summary>
    public bool IsSystemCode { get; set; }

    /// <summary>
    /// 是否默认 BOM
    /// </summary>
    public bool IsDefaultBom { get; set; }

    /// <summary>
    /// BOM 版本
    /// </summary>
    public string BomVersion { get; set; }

    /// <summary>
    /// 产品名称（必填，关联物料）
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// 产品编号
    /// </summary>
    public string ProductCode { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string Specification { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 日产量
    /// </summary>
    public int DailyOutput { get; set; }

    /// <summary>
    /// 备注信息
    /// </summary>
    public string Remarks { get; set; }

    /// <summary>
    /// 工艺路线Id（显示用）
    /// </summary>
    public string ProcessRouteId { get; set; }

    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            if (_isSelected != value)
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }
    }

    public BomProduct()
    {
        BomCode = string.Empty;
        BomVersion = string.Empty;
        ProductCode = string.Empty;
        Specification = string.Empty;
        Unit = string.Empty;
        Remarks = string.Empty;
        ProcessRouteId = string.Empty;
    }

    public event PropertyChangedEventHandler PropertyChanged;
    protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
}