<Window x:Class="WpfApp.Bom.ProcessRouteSelectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="选择工艺路线" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>
    
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 数据表格区域 -->
        <Border Grid.Row="0" Background="White" Margin="20,20,20,10">
            <Grid>
                <!-- 加载指示器 -->
                <TextBlock Text="正在加载..." FontSize="16" Foreground="#666"
                           HorizontalAlignment="Center" VerticalAlignment="Center"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- 数据表格 -->
                <DataGrid ItemsSource="{Binding ProcessRoutes}"
                      SelectedItem="{Binding SelectedProcessRoute}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      CanUserReorderColumns="False"
                      CanUserResizeColumns="True"
                      CanUserSortColumns="False"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionMode="Single"
                      Background="White"
                      BorderThickness="0"
                      RowHeight="45">
                
                <DataGrid.ColumnHeaderStyle>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#F8F9FA"/>
                        <Setter Property="Foreground" Value="#333"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Padding" Value="15,10"/>
                        <Setter Property="BorderBrush" Value="#E0E0E0"/>
                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    </Style>
                </DataGrid.ColumnHeaderStyle>

                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="BorderBrush" Value="#F0F0F0"/>
                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F5F5F5"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>

                <DataGrid.CellStyle>
                    <Style TargetType="DataGridCell">
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Padding" Value="15,10"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                        <Setter Property="HorizontalAlignment" Value="Center"/>
                        <Setter Property="FontSize" Value="13"/>
                        <Style.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="Foreground" Value="#333"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.CellStyle>

                <DataGrid.Columns>
                    <!-- 选择列 -->
                    <DataGridTemplateColumn Header="选择" Width="60">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <RadioButton IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           GroupName="ProcessRouteSelection"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 序号列 -->
                    <DataGridTextColumn Header="序号" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="RowNumber"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <!-- 工艺路线名称列 -->
                    <DataGridTextColumn Header="工艺路线名称" Width="200" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="ProcessRouteName"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <!-- 工艺路线编号列 -->
                    <DataGridTextColumn Header="工艺路线编号" Width="200" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="ProcessRouteCode"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <!-- 工艺路线说明列 -->
                    <DataGridTextColumn Header="工艺路线说明" Width="*" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="Description"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>
                </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- 分页区域 -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,0,20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 分页信息 -->
                <TextBlock Grid.Column="0" Text="{Binding PageInfo}" 
                           VerticalAlignment="Center" FontSize="14" Foreground="#666"/>

                <!-- 分页按钮 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="&lt;" Command="{Binding PreviousPageCommand}"
                            Width="30" Height="30" Margin="0,0,5,0"
                            Background="White" BorderBrush="#E0E0E0" BorderThickness="1"/>

                    <TextBlock Text="{Binding CurrentPage}" VerticalAlignment="Center"
                               Margin="10,0" FontSize="14" FontWeight="Medium"/>

                    <Button Content="&gt;" Command="{Binding NextPageCommand}"
                            Width="30" Height="30" Margin="5,0,0,0"
                            Background="White" BorderBrush="#E0E0E0" BorderThickness="1"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 按钮区域 -->
        <Border Grid.Row="2" Background="White" Padding="20" Margin="20,0,20,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="取消" Command="{Binding CancelCommand}"
                        Width="80" Height="35" Margin="0,0,10,0"
                        Background="White" BorderBrush="#E0E0E0" BorderThickness="1"
                        Foreground="#666" FontSize="14"/>
                
                <Button Content="确定" Command="{Binding ConfirmCommand}"
                        Width="80" Height="35"
                        Background="#1976D2" BorderThickness="0"
                        Foreground="White" FontSize="14" FontWeight="Medium"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
