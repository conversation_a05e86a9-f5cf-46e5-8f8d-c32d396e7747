<Application x:Class="WpfApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"  
             xmlns:local="clr-namespace:WpfApp"
             xmlns:plan="clr-namespace:WpfApp.Plan"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light"  
                                             PrimaryColor="DeepPurple"  
                                             SecondaryColor="Lime" />  
                <ResourceDictionary  
                    Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign3.Defaults.xaml" />  
            </ResourceDictionary.MergedDictionaries>

            
            <!-- 值转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <plan:BoolToYesNoConverter x:Key="BoolToYesNoConverter"/>
            <plan:RowNumberConverter x:Key="RowNumberConverter"/>
            <plan:SelectedPageBackgroundConverter x:Key="SelectedPageBackgroundConverter"/>
            <plan:SelectedPageForegroundConverter x:Key="SelectedPageForegroundConverter"/>
            <plan:PageSizeConverter x:Key="PageSizeConverter"/>
          
        </ResourceDictionary>  
    </Application.Resources>  

</Application>
