<Window x:Class="WpfApp.ProcessRoute.AddProcessRouteWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="新增工艺路线" Height="1000" Width="1400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        WindowState="Maximized">

    <Window.Resources>
        <!-- 数量到可见性转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <Style x:Key="EmptyDataTextStyle" TargetType="TextBlock">
            <Setter Property="Visibility" Value="Collapsed"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=DataGrid}, Path=Items.Count}" Value="0">
                    <Setter Property="Visibility" Value="Visible"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    
    <Grid Margin="0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="新增工艺路线" FontSize="24" FontWeight="Bold"
                   HorizontalAlignment="Center" Margin="0,10,0,10"
                   Foreground="Black" Padding="0,10"/>

        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto"
                      Padding="10" Background="#F5F5F5">
            <StackPanel>
                
                <!-- 基础信息区域 -->
                <materialDesign:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="基础信息" FontSize="16" FontWeight="Bold" 
                                   Foreground="#2196F3" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 第一行 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,0,15">
                                <TextBlock Text="工艺路线编号" Margin="0,0,0,5"/>
                                <TextBox materialDesign:HintAssist.Hint="请输入" 
                                         Text="{Binding ProcessRouteCode}"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,15">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <TextBlock Text="系统编号"/>
                                    <ToggleButton IsChecked="{Binding IsSystemGenerated}" 
                                                  Margin="10,0,0,0"/>
                                    <TextBlock Text="工艺路线名称" Margin="20,0,0,0"/>
                                </StackPanel>
                                <TextBox materialDesign:HintAssist.Hint="请输入" 
                                         Text="{Binding ProcessRouteName}"/>
                            </StackPanel>

                            <!-- 第二行 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3" Margin="0,0,0,15">
                                <TextBlock Text="状态" Margin="0,0,0,5"/>
                                <StackPanel Orientation="Horizontal">
                                    <RadioButton Content="启用" IsChecked="{Binding IsEnabled}" 
                                                 GroupName="Status" Margin="0,0,20,0"/>
                                    <RadioButton Content="禁用" IsChecked="{Binding IsDisabled}" 
                                                 GroupName="Status"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- 第三行 -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3">
                                <TextBlock Text="说明" Margin="0,0,0,5"/>
                                <TextBox materialDesign:HintAssist.Hint="请输入" 
                                         Text="{Binding Description}" 
                                         Height="60" TextWrapping="Wrap" 
                                         AcceptsReturn="True"/>
                            </StackPanel>
                        </Grid>

                        <StackPanel Margin="0,15,0,0">
                            <TextBlock Text="备注" Margin="0,0,0,5"/>
                            <TextBox materialDesign:HintAssist.Hint="请输入" 
                                     Text="{Binding Remarks}" 
                                     Height="60" TextWrapping="Wrap" 
                                     AcceptsReturn="True"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 工序编辑区域 -->
                <materialDesign:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <materialDesign:PackIcon Kind="Settings" 
                                                     Foreground="#FFC107" 
                                                     VerticalAlignment="Center" 
                                                     Margin="0,0,5,0"/>
                            <TextBlock Text="工序组成" FontSize="16" FontWeight="Bold" 
                                       Foreground="#FFC107" VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Button Content="新增" Command="{Binding AddProcessCommand}"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Background="#2196F3" Foreground="White" Margin="0,0,10,0"/>
                            <Button Content="编辑" Command="{Binding EditProcessCommand}"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,10,0"/>
                            <Button Content="删除" Command="{Binding DeleteProcessCommand}"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,10,0"/>
                            <Button Content="加载工序组成" Command="{Binding LoadDataCommand}"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Background="#4CAF50" Foreground="White"/>
                        </StackPanel>

                        <DataGrid ItemsSource="{Binding ProcessSteps}"
                                  SelectedItem="{Binding SelectedProcessStep}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  Height="150">
                            <DataGrid.Columns>
                                <DataGridCheckBoxColumn Header="" Width="40"/>
                                <DataGridTextColumn Header="序号" Width="60" Binding="{Binding Sequence}"/>
                                <DataGridTextColumn Header="工序编号" Width="100" Binding="{Binding ProcessCode}"/>
                                <DataGridTextColumn Header="工序名称" Width="120" Binding="{Binding ProcessName}"/>
                                <DataGridTextColumn Header="下一道工序" Width="100" Binding="{Binding NextProcess}"/>
                                <DataGridTextColumn Header="与下一道工序关系" Width="120" Binding="{Binding Relationship}"/>
                                <DataGridTextColumn Header="关键工序" Width="80" Binding="{Binding IsKeyProcess}"/>
                                <DataGridTextColumn Header="准备时间" Width="80" Binding="{Binding PrepareTime}"/>
                                <DataGridTextColumn Header="等待时间" Width="80" Binding="{Binding WaitTime}"/>
                                <DataGridTextColumn Header="颜色" Width="60" Binding="{Binding Color}"/>
                                <DataGridTextColumn Header="操作" Width="80"/>
                            </DataGrid.Columns>
                        </DataGrid>

                        <TextBlock Text="暂无数据" HorizontalAlignment="Center"
                                   Margin="0,20,0,0" Foreground="Gray"
                                   Style="{StaticResource EmptyDataTextStyle}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 工艺路线管理区域 - 与左边页面完全相同 -->
                <materialDesign:Card Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 标题区域 -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                            <materialDesign:PackIcon Kind="MapMarkerPath"
                                                     Foreground="#673AB7"
                                                     VerticalAlignment="Center"
                                                     Margin="0,0,5,0"/>
                            <TextBlock Text="工艺路线管理" FontSize="16" FontWeight="Bold"
                                       Foreground="#673AB7" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- 查询条件区域 -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
                            <!-- 工艺路线ID标签 -->
                            <TextBlock Text="工艺路线ID:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <!-- 工艺路线ID输入框 -->
                            <TextBox Text="{Binding ProcessRouteSearchId}" Width="200" Margin="0,0,20,0"
                                     materialDesign:HintAssist.Hint="请输入工艺路线ID"/>
                            <!-- 查询按钮 -->
                            <Button Content="查询" Command="{Binding SearchProcessRouteCommand}"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Background="#673AB7" Foreground="White" Margin="0,0,10,0"/>
                            <!-- 清空按钮 -->
                            <Button Content="清空" Command="{Binding ClearSearchCommand}"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,10,0"/>
                            <!-- 刷新按钮 -->
                            <Button Content="刷新" Command="{Binding RefreshProcessRouteCommand}"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" Margin="0,0,10,0"/>
                            <!-- 新增按钮 -->
                            <Button Content="新增工艺路线" Command="{Binding AddNewProcessRouteCommand}"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Background="#4CAF50" Foreground="White"/>
                        </StackPanel>

                        <!-- 数据表格区域 -->
                        <DataGrid Grid.Row="2" ItemsSource="{Binding ProcessCompositions}"
                                  SelectedItem="{Binding SelectedProcessComposition}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  SelectionMode="Single"
                                  AlternatingRowBackground="#F5F5F5"
                                  Height="250">
                            <!-- 定义数据表格列 -->
                            <DataGrid.Columns>
                                <!-- 序号列 -->
                                <DataGridTextColumn Header="序号" Width="60" Binding="{Binding SerialCode}"/>
                                <!-- 工序脚本ID列 -->
                                <DataGridTextColumn Header="工序脚本ID" Width="120" Binding="{Binding ProcessScriptId}"/>
                                <!-- 下一工序列 -->
                                <DataGridTextColumn Header="下一工序" Width="120" Binding="{Binding NextProcedure}"/>
                                <!-- 工艺路线ID列 -->
                                <DataGridTextColumn Header="工艺路线ID" Width="150" Binding="{Binding ProcessRouteId}"/>
                                <!-- 状态列 -->
                                <DataGridTextColumn Header="状态" Width="80" Binding="{Binding StatusDisplay}"/>
                                <!-- 备注列 -->
                                <DataGridTextColumn Header="备注" Width="150" Binding="{Binding Remarks}"/>
                                <!-- 创建时间列 -->
                                <DataGridTextColumn Header="创建时间" Width="140" Binding="{Binding CreateTimeDisplay}"/>
                                <!-- 操作列 - 使用模板列定义操作按钮 -->
                                <DataGridTemplateColumn Header="操作" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <!-- 水平排列的操作按钮容器 -->
                                            <StackPanel Orientation="Horizontal">
                                                <!-- 编辑按钮 -->
                                                <Button Content="编辑"
                                                        Command="{Binding DataContext.EditProcessCompositionCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                        CommandParameter="{Binding}"
                                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Height="25" Margin="2"/>
                                                <!-- 删除按钮 -->
                                                <Button Content="删除"
                                                        Command="{Binding DataContext.DeleteProcessCompositionCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                        CommandParameter="{Binding}"
                                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Height="25" Margin="2"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </materialDesign:Card>

                <!-- 工序组成显示区域 -->
                <materialDesign:Card Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <materialDesign:PackIcon Kind="ViewList"
                                                     Foreground="#FF9800"
                                                     VerticalAlignment="Center"
                                                     Margin="0,0,5,0"/>
                            <TextBlock Text="工序组成" FontSize="16" FontWeight="Bold"
                                       Foreground="#FF9800" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- 筛选条件 -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="工艺路线ID:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBox Text="{Binding ProcessRouteIdFilter}"
                                     materialDesign:HintAssist.Hint="输入工艺路线ID进行筛选"
                                     Width="200" Margin="0,0,10,0"/>
                            <Button Content="筛选" Command="{Binding LoadDataCommand}"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"/>
                        </StackPanel>

                        <DataGrid ItemsSource="{Binding ProcessCompositions}"
                                  SelectedItem="{Binding SelectedProcessComposition}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  Height="180">
                            <DataGrid.Columns>
                                <DataGridCheckBoxColumn Header="" Width="40"/>
                                <DataGridTextColumn Header="序列号" Width="80" Binding="{Binding SerialCode}"/>
                                <DataGridTextColumn Header="工序脚本ID" Width="100" Binding="{Binding ProcessScriptId}"/>
                                <DataGridTextColumn Header="下一工序" Width="100" Binding="{Binding NextProcedure}"/>
                                <DataGridTextColumn Header="关系" Width="80" Binding="{Binding Relationship}"/>
                                <DataGridCheckBoxColumn Header="关键工序" Width="80" Binding="{Binding IsKeyProcess}"/>
                                <DataGridTextColumn Header="准备时间" Width="80" Binding="{Binding PreparationTime}"/>
                                <DataGridTextColumn Header="等待时间" Width="80" Binding="{Binding WaitingTime}"/>
                                <DataGridTextColumn Header="备注" Width="120" Binding="{Binding Remarks}"/>
                                <DataGridTextColumn Header="工艺路线ID" Width="100" Binding="{Binding ProcessRouteId}"/>
                                <DataGridTextColumn Header="创建时间" Width="120" Binding="{Binding CreateTime, StringFormat=yyyy-MM-dd HH:mm}"/>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- 工序组成数据为空时显示提示 -->
                        <TextBlock Text="工序组成数据已加载，包含测试数据"
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"
                                   Foreground="Green"
                                   FontSize="12"
                                   FontStyle="Italic"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 关联产品区域 -->
                <materialDesign:Card Padding="20">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <materialDesign:PackIcon Kind="Package" 
                                                     Foreground="#2196F3" 
                                                     VerticalAlignment="Center" 
                                                     Margin="0,0,5,0"/>
                            <TextBlock Text="关联产品" FontSize="16" FontWeight="Bold" 
                                       Foreground="#2196F3" VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Button Content="添加" Command="{Binding AddProductCommand}"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Background="#2196F3" Foreground="White" Margin="0,0,10,0"/>
                            <Button Content="移除" Command="{Binding RemoveProductCommand}"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"/>
                        </StackPanel>

                        <DataGrid ItemsSource="{Binding RelatedProducts}" 
                                  SelectedItem="{Binding SelectedProduct}"
                                  AutoGenerateColumns="False" 
                                  CanUserAddRows="False"
                                  Height="200">
                            <DataGrid.Columns>
                                <DataGridCheckBoxColumn Header="" Width="40"/>
                                <DataGridTextColumn Header="序号" Width="60" Binding="{Binding Sequence}"/>
                                <DataGridTextColumn Header="产品编号" Width="120" Binding="{Binding ProductCode}"/>
                                <DataGridTextColumn Header="产品名称" Width="150" Binding="{Binding ProductName}"/>
                                <DataGridTextColumn Header="规格型号" Width="120" Binding="{Binding Specification}"/>
                                <DataGridTextColumn Header="单位" Width="80" Binding="{Binding Unit}"/>
                                <DataGridTextColumn Header="BOM编号" Width="120" Binding="{Binding BomCode}"/>
                                <DataGridTextColumn Header="BOM版本" Width="100" Binding="{Binding BomVersion}"/>
                                <DataGridTextColumn Header="操作" Width="80"/>
                            </DataGrid.Columns>
                        </DataGrid>

                        <TextBlock Text="暂无数据" HorizontalAlignment="Center" 
                                   Margin="0,20,0,0" Foreground="Gray"/>
                    </StackPanel>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Content="关闭" Command="{Binding CancelCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}" 
                    Width="80" Margin="0,0,10,0"/>
            <Button Content="确定" Command="{Binding SaveCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="#2196F3" Foreground="White" Width="80" Click="Button_Click"/>
        </StackPanel>
    </Grid>
</Window>
