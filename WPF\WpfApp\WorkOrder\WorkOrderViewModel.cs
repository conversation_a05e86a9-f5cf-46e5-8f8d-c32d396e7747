﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Net.Http;
using System.Windows;
using System.Windows.Input;


namespace WpfApp.WorkOrder
{
    public class WorkOrderViewModel : INotifyPropertyChanged
    {
        private int _page = 1;
        private int _pagesize = 10;
        private int _total;
        private int _totalPages;
        private string _workOrderCode = "";
        private string _planName = "";
        private string _productName = "";
        private int? _status = null;
        private string _statusText = "全部";
        private ObservableCollection<WorkOrderModel> _workOrders= new ObservableCollection<WorkOrderModel>();


        public int Page
        {
            get => _page;
            set { if (_page != value) { _page = value;OnPropertyChanged(nameof(Page)); } ShowData(); }
        }

        public int PageSize
        {
            get => _pagesize;
            set { if (_pagesize != value) { _pagesize = value;OnPropertyChanged(nameof(PageSize)); } ShowData(); }
        }

        public int Total
        {
            get => _total;
            set { if (_total != value) { _total = value;OnPropertyChanged(nameof(Total)); } }
        }

        public int TotalPages
        {
            get => _totalPages;
            set { if (_totalPages != value) { _totalPages = value;OnPropertyChanged(nameof(TotalPages)); } }
        }

        public string WorkOrderCode
        {
            get => _workOrderCode;
            set { if (_workOrderCode != value) { _workOrderCode = value;OnPropertyChanged(nameof(WorkOrderCode)); } ShowData(); }
        }

        public string StatusText
        {
            get => _statusText;
            set
            {
                if (_statusText != value)
                {
                    _statusText = value;
                    Status = value switch
                    {
                        "待排产" => 0,
                        "未开始" => 1,
                        "进行中" => 2,
                        "已完成" => 3,
                        "已撤回" => 4,
                        "已取消" => 5,
                        _ => null
                    };
                    OnPropertyChanged(nameof(StatusText));
                    ShowData();
                }
            }
        }

        public string PlanName
        {
            get => _planName;
            set { if (_planName != value) { _planName = value;OnPropertyChanged(nameof(PlanName)); } ShowData(); }
        }

        public string ProductName
        {
            get => _productName;
            set { if (_productName != value) { _productName = value;OnPropertyChanged(nameof(ProductName)); } ShowData(); }
        }

        public int? Status
        {
            get => _status;
            set { if (_status != value) { _status = value;OnPropertyChanged(nameof(Status)); } ShowData(); }
        }

        public ObservableCollection<WorkOrderModel> WorkOrders
        {
            get => _workOrders;
            set { _workOrders = value; OnPropertyChanged(nameof(WorkOrders)); }
        }

        public ICommand FirstPageCommand { get; }
        public ICommand PrevPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand LastPageCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ResetCommand { get; }
        public ICommand ViewCommand { get; }
        public ICommand ScheduleCommand { get; }

        public WorkOrderViewModel()
        {
            FirstPageCommand = new RelayCommand(_ => { if (Page > 1) Page--; });
            PrevPageCommand = new RelayCommand(_ => { if (Page > 1) Page--; });
            NextPageCommand = new RelayCommand(_ => { if (Page * PageSize < Total) Page++; });
            LastPageCommand = new RelayCommand(_ => Page = (Total + PageSize - 1) / PageSize);
            ShowData();
            SearchCommand = new RelayCommand(_ => ShowData());
            ResetCommand = new RelayCommand(_ =>
            { 
                WorkOrderCode = "";
                PlanName = "";
                ProductName = "";
                StatusText = "全部";
                Page = 1;
                ShowData();
            });
            ScheduleCommand = new RelayCommand(ScheduleWorkOrder);
            ViewCommand = new RelayCommand(ViewWorkOrder);
        }

        // 添加事件
        public event Action<long> ScheduleRequested;

        private void ScheduleWorkOrder(object parameter)
        {
            if (parameter is WorkOrderModel workOrder)
            {
                //触发页面跳转事件，传递工单id
                ScheduleRequested?.Invoke(workOrder.WorkOrderId);
            }
        }

        //点击查看工单详情事件

        public event Action<WorkOrderModel> ViewRequested;
        private void ViewWorkOrder(object parameter)
        {
            if(parameter is WorkOrderModel workOrder)
            {
                ViewRequested?.Invoke(workOrder);
            }
        }

        //显示列表
        private async void ShowData()
        {
            try
            {
                using var client = new HttpClient();
                if(!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var queryParams = new List<string>
                {
                    $"Index={Page}",
                    $"Size={PageSize}"
                };

                if (!string.IsNullOrEmpty(WorkOrderCode))
                    queryParams.Add($"WorkOrderCode={Uri.EscapeDataString(WorkOrderCode)}");
                if (!string.IsNullOrEmpty(PlanName))
                    queryParams.Add($"PlanName={Uri.EscapeDataString(PlanName)}");
                if (!string.IsNullOrEmpty(ProductName))
                    queryParams.Add($"ProductName={Uri.EscapeDataString(ProductName)}");
                if (Status.HasValue)
                    queryParams.Add($"Status={Status.Value}");

                var url = $"http://localhost:5005/api/workOrder/workOrder?{string.Join("&", queryParams)}";
                var json = await client.GetStringAsync(url);
                var options = new System.Text.Json.JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                };

                var result = System.Text.Json.JsonSerializer.Deserialize<WorkOrderPageResponse>(json, options);
                var items = result?.Result?.Items ?? new List<WorkOrderModel>();

                // 设置序号
                for (int i = 0; i < items.Count; i++)
                {
                    items[i].RowIndex = (Page - 1) * PageSize + i + 1;
                    // 根据工序ID字符串生成ProcessSteps
                    if (!string.IsNullOrEmpty(items[i].ProcessStepIds))
                    {
                        var stepIds = items[i].ProcessStepIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                        items[i].ProcessSteps = stepIds.Select((id, index) =>
                            new ProcessStep
                            {
                                StepName = $"工序{id.Trim()}",
                                IsCompleted = index < 2 // 前两个工序默认完成
                            }).ToList();
                    }
                    else
                    {
                        // 默认工序
                        items[i].ProcessSteps = new List<ProcessStep>
                        {
                            new ProcessStep { StepName = "工序1", IsCompleted = true },
                            new ProcessStep { StepName = "工序2", IsCompleted = false }
                        };
                    }

                    // 计算进度百分比
                    var completedSteps = items[i].ProcessSteps.Count(s => s.IsCompleted);
                    items[i].ProgressPercentage = items[i].ProcessSteps.Count > 0 ?
                        (int)((double)completedSteps / items[i].ProcessSteps.Count * 100) : 0;
                }
                WorkOrders = new ObservableCollection<WorkOrderModel>(items);
                Total = result?.Result?.Total ?? 0;
                TotalPages = result?.Result?.TotalPages ?? 0;
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(ex.Message);
                throw;
            }
        }
        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name)=> PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }


    public class WorkOrderPageResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public WorkOrderPageResult Result { get; set; }
    }
    public class WorkOrderPageResult
    {
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int Total { get; set; }
        public int TotalPages { get; set; }
        public List<WorkOrderModel> Items { get; set; }
        public bool HasPrevPage { get; set; }
        public bool HasNextPage { get; set; }
    }
}
