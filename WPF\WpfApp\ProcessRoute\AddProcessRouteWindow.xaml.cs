using System.Windows;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 新增工艺路线窗口
    /// </summary>
    public partial class AddProcessRouteWindow : Window
    {
        public AddProcessRouteWindow()
        {
            InitializeComponent();
            this.DataContext = new AddProcessRouteViewModel();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            // 这个事件处理器可以移除，因为按钮已经绑定了SaveCommand
            // 如果需要额外的处理逻辑，可以在这里添加
        }
    }
}
