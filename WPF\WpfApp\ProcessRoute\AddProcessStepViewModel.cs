using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using WpfApp.Common;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 添加工序视图模型
    /// </summary>
    public class AddProcessStepViewModel : INotifyPropertyChanged
    {
        private readonly HttpClient _httpClient;
        private string _sequence;
        private ProcessItem _selectedProcess;
        private ProcessItem _selectedNextProcess; // 改回ProcessItem类型用于UI绑定
        private string _selectedRelationship;
        private bool _isKeyProcess;
        private string _displayColor;
        private string _prepareTime;
        private string _waitTime;
        private string _remarks;
        private string _processRouteId;

        public AddProcessStepViewModel()
        {
            _httpClient = new HttpClient();

            // 初始化数据
            InitializeData();

            // 初始化命令
            ConfirmCommand = new RelayCommand(_ => ConfirmAdd());
            CancelCommand = new RelayCommand(_ => Cancel());
        }

        /// <summary>
        /// 带工艺路线ID的构造函数
        /// </summary>
        /// <param name="processRouteId">工艺路线ID</param>
        public AddProcessStepViewModel(string processRouteId) : this()
        {
            ProcessRouteId = processRouteId;
        }

        #region 属性

        /// <summary>
        /// 序号
        /// </summary>
        public string Sequence
        {
            get => _sequence;
            set { _sequence = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 可用工序列表
        /// </summary>
        public ObservableCollection<ProcessItem> AvailableProcesses { get; set; } = new();

        /// <summary>
        /// 选中的工序
        /// </summary>
        public ProcessItem SelectedProcess
        {
            get => _selectedProcess;
            set { _selectedProcess = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 选中的下一道工序
        /// </summary>
        public ProcessItem SelectedNextProcess
        {
            get => _selectedNextProcess;
            set { _selectedNextProcess = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 可用关系列表
        /// </summary>
        public ObservableCollection<string> AvailableRelationships { get; set; } = new()
        {
            "S_S",
            "F_F",
            "S_F",
            "F_S"
        };

        /// <summary>
        /// 选中的关系
        /// </summary>
        public string SelectedRelationship
        {
            get => _selectedRelationship;
            set { _selectedRelationship = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 是否关键工序
        /// </summary>
        public bool IsKeyProcess
        {
            get => _isKeyProcess;
            set { _isKeyProcess = value; OnPropertyChanged(); OnPropertyChanged(nameof(IsNotKeyProcess)); }
        }

        /// <summary>
        /// 不是关键工序
        /// </summary>
        public bool IsNotKeyProcess
        {
            get => !_isKeyProcess;
            set { _isKeyProcess = !value; OnPropertyChanged(); OnPropertyChanged(nameof(IsKeyProcess)); }
        }



        /// <summary>
        /// 准备时间
        /// </summary>
        public string PrepareTime
        {
            get => _prepareTime;
            set { _prepareTime = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 等待时间
        /// </summary>
        public string WaitTime
        {
            get => _waitTime;
            set { _waitTime = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks
        {
            get => _remarks;
            set { _remarks = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        public string ProcessRouteId
        {
            get => _processRouteId;
            set { _processRouteId = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 显示颜色
        /// </summary>
        public string DisplayColor
        {
            get => _displayColor;
            set { _displayColor = value; OnPropertyChanged(); }
        }

        #endregion

        #region 命令

        public ICommand ConfirmCommand { get; }
        public ICommand CancelCommand { get; }

        #endregion

        #region 事件

        /// <summary>
        /// 工序添加成功事件
        /// </summary>
        public event Action<ProcessStepItem> ProcessAdded;

        #endregion

        #region 方法

        /// <summary>
        /// 初始化数据
        /// </summary>
        private async void InitializeData()
        {
            await LoadAvailableProcesses();
        }

        /// <summary>
        /// 加载可用工序列表
        /// </summary>
        private async Task LoadAvailableProcesses()
        {
            try
            {
                // 设置认证头
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    _httpClient.DefaultRequestHeaders.Clear();
                    _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var response = await _httpClient.GetAsync("http://localhost:5005/api/processRoute/processStepList");
                var jsonString = await response.Content.ReadAsStringAsync();

                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var result = JsonSerializer.Deserialize<ApiResult<List<ProcessStep>>>(jsonString, jsonOptions);
                
                if (result?.Code == 200 && result.Result != null)
                {
                    AvailableProcesses.Clear();
                    foreach (var process in result.Result)
                    {
                        AvailableProcesses.Add(new ProcessItem
                        {
                            ProcessStepId = process.ProcessScriptId, // API返回ProcessScriptId，对应数据库processstepid
                            ProcessCode = process.ProcessCode,
                            ProcessName = process.ProcessName
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载工序列表失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 确认添加
        /// </summary>
        private async void ConfirmAdd()
        {
            // 验证必填字段
            if (SelectedProcess == null)
            {
                MessageBox.Show("请选择工序！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(Sequence))
            {
                MessageBox.Show("请输入序号！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // 测试完整数据流（调试用）
                TestCompleteDataFlow();

                // 保存到数据库
                var success = await SaveToDatabase(Sequence);

                if (success)
                {
                    // 创建工序项
                    var processStep = new ProcessStepItem
                    {
                        Sequence = int.TryParse(Sequence, out int seqNum) ? seqNum : 1, // 用于UI显示的序号
                        ProcessCode = SelectedProcess?.ProcessCode ?? "",
                        ProcessName = SelectedProcess?.ProcessName ?? "",
                        NextProcess = SelectedNextProcess?.ProcessName ?? "无",
                        Relationship = SelectedRelationship ?? "",
                        IsKeyProcess = IsKeyProcess ? "是" : "否",
                        PrepareTime = PrepareTime ?? "",
                        WaitTime = WaitTime ?? ""
                    };

                    // 触发添加事件
                    ProcessAdded?.Invoke(processStep);

                    MessageBox.Show("工序添加成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);

                    // 关闭窗口
                    CloseWindow();
                }
            }
            catch (HttpRequestException httpEx)
            {
                MessageBox.Show($"网络请求失败: {httpEx.Message}", "网络错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (JsonException jsonEx)
            {
                MessageBox.Show($"数据格式错误: {jsonEx.Message}", "JSON错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存工序失败: {ex.Message}\n\n详细信息: {ex}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消
        /// </summary>
        private void Cancel()
        {
            CloseWindow();
        }

        /// <summary>
        /// 保存工序组成到数据库
        /// </summary>
        /// <param name="serialCode">序号（varchar类型）</param>
        /// <returns>是否保存成功</returns>
        private async Task<bool> SaveToDatabase(string serialCode)
        {
            try
            {
                // 设置认证头
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    _httpClient.DefaultRequestHeaders.Clear();
                    _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                // 创建请求数据，根据后端接口字段要求
                var requestData = new
                {
                    processRouteId = ProcessRouteId ?? "1",
                    serialCode = serialCode, // 直接使用字符串类型的序号
                    processScriptId = SelectedProcess?.ProcessStepId ?? 0, // 使用工序表的processstepid
                    nextProcedure = SelectedNextProcess?.ProcessStepId ?? 0, // 使用下一道工序的processstepid
                    relationship = SelectedRelationship ?? "",
                    isKeyProcess = IsKeyProcess,
                    displayColor = DisplayColor ?? "",
                    preparationTime = PrepareTime ?? "",
                    waitingTime = int.TryParse(WaitTime, out int waitTimeInt) ? waitTimeInt : 0,
                    remarks = Remarks ?? ""
                };

                // 序列化请求数据
                var jsonContent = JsonSerializer.Serialize(requestData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true // 格式化JSON以便调试
                });

                // 调试信息：显示发送的JSON数据和ID绑定情况
                System.Diagnostics.Debug.WriteLine("=== API请求数据映射 ===");
                System.Diagnostics.Debug.WriteLine($"序号(varchar): {serialCode}");
                System.Diagnostics.Debug.WriteLine($"当前工序 - ID: {SelectedProcess?.ProcessStepId}(int), 名称: {SelectedProcess?.ProcessName}(varchar)");
                System.Diagnostics.Debug.WriteLine($"下一道工序 - ID: {SelectedNextProcess?.ProcessStepId}(int), 名称: {SelectedNextProcess?.ProcessName}(varchar)");
                System.Diagnostics.Debug.WriteLine($"processScriptId字段传递: {SelectedProcess?.ProcessStepId ?? 0}");
                System.Diagnostics.Debug.WriteLine($"nextProcedure字段传递: {SelectedNextProcess?.ProcessStepId ?? 0}");
                System.Diagnostics.Debug.WriteLine($"发送的JSON数据: {jsonContent}");
                System.Diagnostics.Debug.WriteLine("========================");

                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // 发送POST请求
                var response = await _httpClient.PostAsync("http://localhost:5005/api/processRoute/processComposition", content);
                var responseString = await response.Content.ReadAsStringAsync();

                // 调试信息：显示响应数据
                System.Diagnostics.Debug.WriteLine($"响应状态码: {response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"响应内容: {responseString}");

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        // 首先尝试简单解析，只获取基本字段
                        using (var document = JsonDocument.Parse(responseString))
                        {
                            var root = document.RootElement;

                            // 尝试获取code字段
                            if (root.TryGetProperty("code", out var codeElement))
                            {
                                var code = codeElement.GetInt32();

                                if (code == 200)
                                {
                                    return true;
                                }
                                else
                                {
                                    // 尝试获取message字段
                                    string message = "未知错误";
                                    if (root.TryGetProperty("message", out var messageElement))
                                    {
                                        message = messageElement.GetString() ?? "未知错误";
                                    }

                                    MessageBox.Show($"保存失败: {message}", "错误",
                                        MessageBoxButton.OK, MessageBoxImage.Error);
                                    return false;
                                }
                            }
                            else
                            {
                                // 如果没有code字段，假设成功
                                MessageBox.Show("保存成功（响应格式异常但请求已处理）", "提示",
                                    MessageBoxButton.OK, MessageBoxImage.Information);
                                return true;
                            }
                        }
                    }
                    catch (JsonException jsonEx)
                    {
                        MessageBox.Show($"响应数据解析失败: {jsonEx.Message}\n\n响应内容: {responseString}", "JSON解析错误",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }
                else
                {
                    MessageBox.Show($"网络请求失败: {response.StatusCode}\n{responseString}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存到数据库时发生错误: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }



        /// <summary>
        /// 关闭窗口
        /// </summary>
        private void CloseWindow()
        {
            foreach (Window window in Application.Current.Windows)
            {
                if (window.DataContext == this)
                {
                    window.Close();
                    break;
                }
            }
        }

        /// <summary>
        /// 验证工序ID绑定情况（调试用）
        /// </summary>
        public void DebugProcessBinding()
        {
            System.Diagnostics.Debug.WriteLine("=== 工序ID绑定调试信息 ===");
            System.Diagnostics.Debug.WriteLine($"可用工序数量: {AvailableProcesses?.Count ?? 0}");

            if (AvailableProcesses != null)
            {
                foreach (var process in AvailableProcesses)
                {
                    System.Diagnostics.Debug.WriteLine($"工序: ID={process.ProcessStepId}(int), Code={process.ProcessCode}, Name={process.ProcessName}(varchar)");
                }
            }

            System.Diagnostics.Debug.WriteLine($"当前选中工序: ID={SelectedProcess?.ProcessStepId}(int), Name={SelectedProcess?.ProcessName}(varchar)");
            System.Diagnostics.Debug.WriteLine($"下一道工序: ID={SelectedNextProcess?.ProcessStepId}(int), Name={SelectedNextProcess?.ProcessName}(varchar)");
            System.Diagnostics.Debug.WriteLine("========================");
        }

        /// <summary>
        /// 验证processStepId和nextProcedure字段的映射
        /// </summary>
        public void ValidateFieldMapping()
        {
            System.Diagnostics.Debug.WriteLine("=== 字段映射验证 ===");
            System.Diagnostics.Debug.WriteLine("前端显示 → 后端传递映射:");
            System.Diagnostics.Debug.WriteLine($"工序选择框显示: {SelectedProcess?.ProcessName}(varchar) → processScriptId: {SelectedProcess?.ProcessStepId}(int)");
            System.Diagnostics.Debug.WriteLine($"下一道工序选择框显示: {SelectedNextProcess?.ProcessName}(varchar) → nextProcedure: {SelectedNextProcess?.ProcessStepId}(int)");

            // 验证ID是否有效
            bool currentProcessValid = SelectedProcess?.ProcessStepId > 0;
            bool nextProcessValid = SelectedNextProcess?.ProcessStepId > 0;

            System.Diagnostics.Debug.WriteLine($"当前工序ID有效性: {currentProcessValid}");
            System.Diagnostics.Debug.WriteLine($"下一道工序ID有效性: {nextProcessValid}");
            System.Diagnostics.Debug.WriteLine("==================");
        }

        /// <summary>
        /// 测试完整的数据流：从UI显示到API传递
        /// </summary>
        public void TestCompleteDataFlow()
        {
            System.Diagnostics.Debug.WriteLine("=== 完整数据流测试 ===");

            // 1. UI层面 - 用户看到的
            System.Diagnostics.Debug.WriteLine("1. UI层面 - 用户看到的:");
            System.Diagnostics.Debug.WriteLine($"   工序下拉框显示: {SelectedProcess?.ProcessName} (varchar类型)");
            System.Diagnostics.Debug.WriteLine($"   下一道工序下拉框显示: {SelectedNextProcess?.ProcessName} (varchar类型)");

            // 2. 数据绑定层面 - 对象包含的完整信息
            System.Diagnostics.Debug.WriteLine("2. 数据绑定层面 - 对象包含的完整信息:");
            if (SelectedProcess != null)
            {
                System.Diagnostics.Debug.WriteLine($"   当前工序对象: ID={SelectedProcess.ProcessStepId}(int), Code={SelectedProcess.ProcessCode}, Name={SelectedProcess.ProcessName}");
            }
            if (SelectedNextProcess != null)
            {
                System.Diagnostics.Debug.WriteLine($"   下一道工序对象: ID={SelectedNextProcess.ProcessStepId}(int), Code={SelectedNextProcess.ProcessCode}, Name={SelectedNextProcess.ProcessName}");
            }

            // 3. API传递层面 - 发送给后端的数据
            System.Diagnostics.Debug.WriteLine("3. API传递层面 - 发送给后端的数据:");
            System.Diagnostics.Debug.WriteLine($"   processScriptId: {SelectedProcess?.ProcessStepId ?? 0} (int类型 - 工序表主键)");
            System.Diagnostics.Debug.WriteLine($"   nextProcedure: {SelectedNextProcess?.ProcessStepId ?? 0} (int类型 - 工序表主键)");

            // 4. 数据库存储层面
            System.Diagnostics.Debug.WriteLine("4. 数据库存储层面:");
            System.Diagnostics.Debug.WriteLine($"   processcomposition表的processScriptId字段存储: {SelectedProcess?.ProcessStepId ?? 0}");
            System.Diagnostics.Debug.WriteLine($"   processcomposition表的nextProcedure字段存储: {SelectedNextProcess?.ProcessStepId ?? 0}");

            System.Diagnostics.Debug.WriteLine("======================");
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// 工序项目
    /// </summary>
    public class ProcessItem
    {
        public int ProcessStepId { get; set; } // 工序主键ID (对应数据库processstepid字段) - 传递给后端
        public string ProcessCode { get; set; } // 工序编码
        public string ProcessName { get; set; } // 工序名称 - 显示给用户

        /// <summary>
        /// 用于UI显示的完整描述 (编码 - 名称)
        /// </summary>
        public string DisplayText => $"{ProcessCode} - {ProcessName}";

        /// <summary>
        /// 重写ToString方法，确保ComboBox显示工序名称
        /// </summary>
        public override string ToString()
        {
            return ProcessName ?? "";
        }
    }
}
