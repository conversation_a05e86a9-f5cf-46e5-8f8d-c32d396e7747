﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace WpfApp.WorkOrder
{
    /// <summary>
    /// WorkOrderPage.xaml 的交互逻辑
    /// </summary>
    public partial class WorkOrderPage : UserControl
    {
        public WorkOrderPage()
        {
            InitializeComponent();
            DataContext = new WorkOrderViewModel();

            //订阅排产事件
            if(DataContext is WorkOrderViewModel viewModel)
            {
                viewModel.ScheduleRequested += OnScheduleRequested;
                viewModel.ViewRequested += OnViewRequested;
            }
        }

        public event System.Action<long> ScheduleRequested;
        private void OnScheduleRequested(long workOrderId)
        {
            ScheduleRequested?.Invoke(workOrderId);
        }

        public event Action<WorkOrderModel> ViewRequested;
        private void OnViewRequested(WorkOrderModel workOrder)
        {
            ViewRequested?.Invoke(workOrder);
        }
    }
}
