
// 引用系统基础类库
using System;
// 引用WPF输入类库
using System.Windows.Input;
// 引用属性变更通知接口
using System.ComponentModel;
// 引用WPF控件类库
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows;
using System;
// 引用WPF窗口类库
using System.Windows;
// 引用主应用程序命名空间
using WpfApp;
// 引用工艺路线命名空间
using WpfApp.ProcessRoute;
// 引用通用命令类库
using WpfApp.Common;
using WpfApp.WorkOrder;
using WpfApp.Bom;
using WpfApp.Procedure;

using WpfApp.Plan;

/// <summary>
/// 菜单页面视图模型类
/// 实现INotifyPropertyChanged接口，支持数据绑定和属性变更通知
/// 负责处理菜单导航和页面切换的业务逻辑
/// </summary>
public class MenuPageViewModel : INotifyPropertyChanged
{
    // 当前显示视图的私有字段
    private UserControl _currentView;

    /// <summary>
    /// 当前显示的视图属性
    /// 用于在右侧内容区域显示不同的页面
    /// </summary>
    public UserControl CurrentView
    {
        // 获取当前显示的视图
        get => _currentView;
        // 设置当前显示的视图，并触发属性变更通知
        set
        {
            _currentView = value;
            OnPropertyChanged(nameof(CurrentView));
        }
    }

    /// <summary>
    /// 显示物料管理命令
    /// </summary>
    public ICommand ShowMaterialCommand { get; set; }

    /// <summary>
    /// 显示BOM管理命令
    /// </summary>
    public ICommand ShowBomCommand { get; set; }

    /// <summary>
    /// 显示系统设置命令
    /// </summary>
    public ICommand ShowSettingsCommand { get; set; }

    /// <summary>
    /// 显示首页命令
    /// </summary>
    public ICommand ShowHomeCommand { get; set; }

    /// <summary>
    /// 显示生产计划命令
    /// </summary>
    public ICommand ShowProductionPlanCommand { get; set; }

    /// <summary>
    /// 显示生产工单命令
    /// </summary>
    public ICommand ShowWorkOrderCommand { get; set; }

    /// <summary>
    /// 显示工单任务命令
    /// </summary>
    public ICommand ShowTaskCommand { get; set; }

    /// <summary>
    /// 显示报工记录命令
    /// </summary>
    public ICommand ShowReportCommand { get; set; }

    /// <summary>
    /// 显示质量检验命令
    /// </summary>
    public ICommand ShowQualityCommand { get; set; }

    /// <summary>
    /// 显示工艺路线命令
    /// </summary>
    public ICommand ShowProcessRouteCommand { get; set; }

    /// <summary>
    /// 显示工序管理命令
    /// </summary>
    public ICommand ShowProcedureCommand { get; set; }

    /// <summary>
    /// 显示车间管理命令
    /// </summary>
    public ICommand ShowWorkshopCommand { get; set; }

    /// <summary>
    /// 显示生产监控命令
    /// </summary>
    public ICommand ShowMonitoringCommand { get; set; }

    /// <summary>
    /// 测试添加工序命令
    /// </summary>
    public ICommand TestAddProcessStepCommand { get; set; }

    /// <summary>
    /// 打开新增工艺路线窗口命令
    /// </summary>
    public ICommand OpenAddProcessRouteWindowCommand { get; set; }

    /// <summary>
    /// 菜单变更事件 - 当菜单项被点击时触发
    /// </summary>
    public event Action<string> MenuChanged;

    /// <summary>
    /// 属性变更事件 - 当属性值发生变化时触发
    /// </summary>
    public event PropertyChangedEventHandler PropertyChanged;

    /// <summary>
    /// 构造函数 - 初始化菜单页面视图模型
    /// 在构造函数中初始化所有菜单命令
    /// </summary>
    public MenuPageViewModel()
    {
        // 初始化首页命令，绑定到ShowHome方法
        ShowHomeCommand = new WpfApp.Bom.RelayCommand(_ => ShowHome());

        // 初始化工艺路线命令，绑定到ShowProcessRoute方法
        ShowProcessRouteCommand = new WpfApp.Common.RelayCommand(_ => ShowProcessRoute());
        // 初始化工序管理命令，绑定到ShowProcedure方法
        ShowProcedureCommand = new WpfApp.Bom.RelayCommand(_ => ShowProcedure());

        // 初始化BOM管理命令，绑定到ShowBom方法
        ShowBomCommand = new WpfApp.Bom.RelayCommand(_ => ShowBom());

        //初始化生产工单命令，绑定到ShowWorkOrder方法
        ShowWorkOrderCommand = new WpfApp.Bom.RelayCommand(_ => ShowWorkOrder());

        //初始化工单任务命令，绑定到ShowTask方法
        ShowTaskCommand = new WpfApp.Bom.RelayCommand(_ => ShowTask());

        //初始化生产计划命令，绑定到ShowProductionPlan方法
        ShowProductionPlanCommand = new WpfApp.Bom.RelayCommand(_ => ShowProductionPlan());

        // 订阅页面导航事件
        ProcessRouteDisplayViewModel.RequestNavigateToAddProcessRoute += ShowAddProcessRoute;
        AddProcessRouteViewModel.RequestReturnToProcessRouteDisplay += ShowProcessRoute;
    }

    /// <summary>
    /// 显示首页的方法
    /// 创建首页占位符视图并触发菜单变更事件
    /// </summary>
    private void ShowHome()
    {
        // 创建首页占位符视图
        CurrentView = CreatePlaceholderView("首页", "欢迎使用MES制造执行系统");
        // 触发菜单变更事件
        MenuChanged?.Invoke("首页");
    }

    /// <summary>
    /// 显示生产计划页面的方法
    /// 创建生产计划占位符视图并触发菜单变更事件
    /// </summary>
    private void ShowProductionPlan()
    {
        // 导航到生产计划页面
        var planPage = new PlanPage();
        planPage.SwitchPage += HandlePlanPageNavigation;
        CurrentView = planPage;
        MenuChanged?.Invoke("生产计划");
    }

    // 处理计划页面的导航请求
    private void HandlePlanPageNavigation(UserControl page)
    {
        try
        {
            if (page != null)
            {
                // 更新当前视图为新页面
                CurrentView = page;
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"已导航到新页面: {page.GetType().Name}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("警告: 尝试导航到空页面");
            }
        }
        catch (Exception ex)
        {
            // 捕获并记录任何导航异常
            System.Diagnostics.Debug.WriteLine($"导航时发生异常: {ex.Message}");
            MessageBox.Show($"导航过程中发生错误: {ex.Message}", "导航错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ShowWorkOrder()
    {
        // 创建生产工单占位符视图
        CurrentView = CreatePlaceholderView("生产工单", "生产工单管理功能开发中...");
        // 触发菜单变更事件
        MenuChanged?.Invoke("生产工单");
    }

    /// <summary>
    /// 显示工单任务页面的方法
    /// 创建工单任务占位符视图并触发菜单变更事件
    /// </summary>
    private void ShowTask()
    {
        // 创建工单任务占位符视图
        CurrentView = CreatePlaceholderView("工单任务", "工单任务管理功能开发中...");
        // 触发菜单变更事件
        MenuChanged?.Invoke("工单任务");
    }

    /// <summary>
    /// 显示报工记录页面的方法
    /// 创建报工记录占位符视图并触发菜单变更事件
    /// </summary>
    private void ShowReport()
    {
        // 创建报工记录占位符视图
        CurrentView = CreatePlaceholderView("报工记录", "报工记录管理功能开发中...");
        // 触发菜单变更事件
        MenuChanged?.Invoke("报工记录");
    }

    /// <summary>
    /// 显示质量检验页面的方法
    /// 创建质量检验占位符视图并触发菜单变更事件
    /// </summary>
    private void ShowQuality()
    {
        // 创建质量检验占位符视图
        CurrentView = CreatePlaceholderView("质量检验", "质量检验管理功能开发中...");
        // 触发菜单变更事件
        MenuChanged?.Invoke("质量检验");
    }

    /// <summary>
    /// 显示工艺路线页面的方法
    /// 创建工艺路线显示页面实例并触发菜单变更事件
    /// </summary>
    private void ShowProcessRoute()
    {
        // 创建工艺路线显示页面实例
        CurrentView = new ProcessRouteDisplayPage();
        // 触发菜单变更事件
        MenuChanged?.Invoke("工艺路线");
    }

    /// <summary>
    /// 显示工序管理页面的方法
    /// 创建工序管理页面实例并设置新的数据上下文，触发菜单变更事件
    /// </summary>
    private void ShowProcedure()
    {
        // 创建工序管理页面实例，使用正确的命名空间
        CurrentView = new WpfApp.Procedure.ProcedurePage();
        // 触发菜单变更事件
        MenuChanged?.Invoke("工序管理");
    }

    /// <summary>
    /// 显示车间管理页面的方法
    /// 创建车间管理占位符视图并触发菜单变更事件
    /// </summary>
    private void ShowWorkshop()
    {
        // 创建车间管理占位符视图
        CurrentView = CreatePlaceholderView("车间管理", "车间管理功能开发中...");
        // 触发菜单变更事件
        MenuChanged?.Invoke("车间管理");
    }

    /// <summary>
    /// 显示生产监控页面的方法
    /// 创建生产监控占位符视图并触发菜单变更事件
    /// </summary>
    private void ShowMonitoring()
    {
        // 创建生产监控占位符视图
        CurrentView = CreatePlaceholderView("生产监控", "生产监控功能开发中...");
        // 触发菜单变更事件
        MenuChanged?.Invoke("生产监控");
    }

    /// <summary>
    /// 显示物料管理页面的方法
    /// 创建物料管理占位符视图并触发菜单变更事件
    /// </summary>
    private void ShowMaterial()
    {
        // 创建物料管理占位符视图
        CurrentView = CreatePlaceholderView("物料管理", "物料管理功能开发中...");
        // 触发菜单变更事件
        MenuChanged?.Invoke("物料管理");
    }

    /// <summary>
    /// 显示BOM管理页面的方法
    /// 创建BOM页面实例并设置数据上下文，触发菜单变更事件
    /// </summary>
    private void ShowBom()
    {
        // 创建BOM页面实例并设置数据上下文
        CurrentView = new BomPage { DataContext = new BomViewModel() };
        // 触发菜单变更事件
        MenuChanged?.Invoke("BOM");
    }


    /// <summary>
    /// 触发属性变更通知的方法
    /// 当属性值发生变化时调用此方法通知UI更新
    /// </summary>
    /// <param name="propertyName">发生变更的属性名</param>
    protected virtual void OnPropertyChanged(string propertyName)
    {
        // 如果有订阅者，触发属性变更事件
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    /// <summary>
    /// 创建占位符视图的辅助方法
    /// 用于为尚未实现的功能页面创建统一的占位符界面
    /// </summary>
    /// <param name="title">页面标题</param>
    /// <param name="description">页面描述信息</param>
    /// <returns>包含标题和描述的用户控件</returns>
    private UserControl CreatePlaceholderView(string title, string description)
    {
        // 创建垂直和水平居中的堆栈面板
        var stackPanel = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        // 创建标题文本块
        var titleBlock = new TextBlock
        {
            Text = title, // 设置标题文本
            FontSize = 32, // 设置字体大小为32
            FontWeight = FontWeights.Bold, // 设置字体粗细为粗体
            HorizontalAlignment = HorizontalAlignment.Center, // 水平居中对齐
            Foreground = System.Windows.Media.Brushes.DarkBlue, // 设置前景色为深蓝色
            Margin = new Thickness(0, 0, 0, 20) // 设置下边距为20像素
        };

        // 创建描述文本块
        var descBlock = new TextBlock
        {
            Text = description, // 设置描述文本
            FontSize = 16, // 设置字体大小为16
            HorizontalAlignment = HorizontalAlignment.Center, // 水平居中对齐
            Foreground = System.Windows.Media.Brushes.Gray // 设置前景色为灰色
        };

        // 将标题文本块添加到堆栈面板
        stackPanel.Children.Add(titleBlock);
        // 将描述文本块添加到堆栈面板
        stackPanel.Children.Add(descBlock);

        // 返回包含堆栈面板的用户控件
        return new UserControl { Content = stackPanel };
    }

    /// <summary>
    /// 显示新增工艺路线页面
    /// </summary>
    private void ShowAddProcessRoute()
    {
        try
        {
            // 创建AddProcessRouteWindow实例
            var addWindow = new AddProcessRouteWindow();

            // 创建一个UserControl来包装Window的内容
            var userControl = new UserControl();

            // 获取Window的内容
            var content = addWindow.Content;
            if (content != null)
            {
                // 从Window中移除内容
                addWindow.Content = null;
                // 设置给UserControl
                userControl.Content = content;
                // 设置DataContext
                userControl.DataContext = new AddProcessRouteViewModel();
            }

            CurrentView = userControl;
            // 触发菜单变更事件
            MenuChanged?.Invoke("新增工艺路线");
        }
        catch (Exception ex)
        {
            // 如果出错，显示错误信息
            CurrentView = CreatePlaceholderView("新增工艺路线", $"加载页面时出错：{ex.Message}");
            MenuChanged?.Invoke("新增工艺路线");
        }
    }
}
