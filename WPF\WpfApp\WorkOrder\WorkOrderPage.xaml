﻿<UserControl x:Class="WpfApp.WorkOrder.WorkOrderPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:local="clr-namespace:WpfApp.WorkOrder"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1400">
    
    <UserControl.Resources>
        <local:StatusToScheduleVisibilityConverter x:Key="StatusToScheduleVisibilityConverter"/>
        <local:StatusToViewVisibilityConverter x:Key="StatusToViewVisibilityConverter"/>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 查询区域 -->
        <Border Grid.Row="0" Background="White" Padding="15" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="请输入工单编号/名称" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox Grid.Column="1" Text="{Binding WorkOrderCode}" Margin="0,0,20,0" Height="30"/>

                <TextBlock Grid.Column="2" Text="请输入工作台编号/名称" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox Grid.Column="3" Text="{Binding PlanName}" Margin="0,0,20,0" Height="30"/>

                <TextBlock Grid.Column="4" Text="选择产品名称" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox Grid.Column="5" Text="{Binding ProductName}" Margin="0,0,20,0" Height="30"/>

                <TextBlock Grid.Column="6" Text="选择状态" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <ComboBox Grid.Column="7" Text="{Binding StatusText}" IsEditable="False" Margin="0,0,20,0" Height="30">
                    <ComboBoxItem>全部</ComboBoxItem>
                    <ComboBoxItem>待排产</ComboBoxItem>
                    <ComboBoxItem>未开始</ComboBoxItem>
                    <ComboBoxItem>进行中</ComboBoxItem>
                    <ComboBoxItem>已完成</ComboBoxItem>
                    <ComboBoxItem>已撤回</ComboBoxItem>
                    <ComboBoxItem>已取消</ComboBoxItem>
                </ComboBox>
                <StackPanel Grid.Column="9" Orientation="Horizontal">
                    <Button Content="查询" Command="{Binding SearchCommand}" Background="#007BFF" Foreground="White" 
                    Padding="15,5" Margin="5,0" BorderThickness="0"/>
                    <Button Content="重置" Command="{Binding ResetCommand}" Background="#6C757D" Foreground="White" 
                    Padding="15,5" Margin="5,0" BorderThickness="0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 标题和操作栏 -->
        <!--<Border Grid.Row="1" Background="#F8F9FA" Padding="15,10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="生产工单管理" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="#007BFF" CornerRadius="3" Margin="5,0">
                        <Button Content="新增工单" Foreground="White" Padding="10,5" BorderThickness="0" Background="Transparent"/>
                    </Border>
                    <Border Background="#28A745" CornerRadius="3" Margin="5,0">
                        <Button Content="导出" Foreground="White" Padding="10,5" BorderThickness="0" Background="Transparent"/>
                    </Border>
                    <Border Background="#6C757D" CornerRadius="3" Margin="5,0">
                        <Button Content="刷新" Foreground="White" Padding="10,5" BorderThickness="0" Background="Transparent"/>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>-->

        <ScrollViewer Grid.Row="1" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
            <!-- 工单列表 -->
            <DataGrid Grid.Row="1" ItemsSource="{Binding WorkOrders}" AutoGenerateColumns="False" 
          CanUserAddRows="False" CanUserDeleteRows="False" 
          GridLinesVisibility="All" HeadersVisibility="All"
          AlternatingRowBackground="#F8F9FA" RowHeight="40"
          ScrollViewer.HorizontalScrollBarVisibility="Auto"
          ScrollViewer.VerticalScrollBarVisibility="Auto">
                <DataGrid.Columns>
                    <!-- 序号 -->
                    <DataGridTextColumn Header="序号" Binding="{Binding RowIndex}" Width="60" IsReadOnly="True"/>

                    <!-- 工单编号 -->
                    <DataGridTextColumn Header="工单编号" Binding="{Binding WorkOrderCode}" Width="120" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="#007BFF"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 工单名称 -->
                    <DataGridTextColumn Header="工单名称" Binding="{Binding WorkOrderName}" Width="150" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Margin" Value="5,0"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 工单进度 -->
                    <DataGridTemplateColumn Header="工单进度" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Grid Margin="5">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 工序步骤格子 -->
                                    <ItemsControl Grid.Row="0" ItemsSource="{Binding ProcessSteps}">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel Orientation="Horizontal"/>
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Width="20" Height="15" Margin="1" CornerRadius="2">
                                                    <Border.Style>
                                                        <Style TargetType="Border">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsCompleted}" Value="True">
                                                                    <Setter Property="Background" Value="#007BFF"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding IsCompleted}" Value="False">
                                                                    <Setter Property="Background" Value="#E9ECEF"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>

                                    <!-- 进度百分比 -->
                                    <TextBlock Grid.Row="1" Text="{Binding ProgressPercentage, StringFormat={}{0}%}" 
                          HorizontalAlignment="Center" FontSize="10" Margin="0,2,0,0"/>
                                </Grid>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 关联计划 -->
                    <DataGridTextColumn Header="关联计划" Binding="{Binding PlanName}" Width="120" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 产品名称 -->
                    <DataGridTextColumn Header="产品名称" Binding="{Binding ProductName}" Width="120" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="Margin" Value="5,0"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 产品编号 -->
                    <DataGridTextColumn Header="产品编号" Binding="{Binding ProductCode}" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 规格型号 -->
                    <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 产品类型 -->
                    <DataGridTextColumn Header="产品类型" Binding="{Binding ProductType}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 单位 -->
                    <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="60" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 需求日期 -->
                    <DataGridTextColumn Header="需求日期" Binding="{Binding DemandTime, StringFormat=yyyy-MM-dd}" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 计划数量 -->
                    <DataGridTextColumn Header="计划数量" Binding="{Binding PlanNumber}" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="#007BFF"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 实际生产数量 -->
                    <DataGridTextColumn Header="实际生产数量" Binding="{Binding RealityNumber}" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="#28A745"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 计划开工时间 -->
                    <DataGridTextColumn Header="计划开工时间" Binding="{Binding PlanStartTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontSize" Value="11"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 计划完工时间 -->
                    <DataGridTextColumn Header="计划完工时间" Binding="{Binding PlanEndTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontSize" Value="11"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 实际开工时间 -->
                    <DataGridTextColumn Header="实际开工时间" Binding="{Binding RealityStartTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontSize" Value="11"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 实际完工时间 -->
                    <DataGridTextColumn Header="实际完工时间" Binding="{Binding RealityEndTime, StringFormat=yyyy-MM-dd HH:mm}" Width="130" IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontSize" Value="11"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- 状态 -->
                    <DataGridTemplateColumn Header="状态" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Status}" Value="0">
                                                    <Setter Property="Background" Value="#FFC107"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="1">
                                                    <Setter Property="Background" Value="#6C757D"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="2">
                                                    <Setter Property="Background" Value="#007BFF"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="3">
                                                    <Setter Property="Background" Value="#28A745"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="4">
                                                    <Setter Property="Background" Value="#DC3545"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="5">
                                                    <Setter Property="Background" Value="#6C757D"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Foreground="White" FontSize="11" FontWeight="Bold" HorizontalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="0">
                                                        <Setter Property="Text" Value="待排产"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="1">
                                                        <Setter Property="Text" Value="未开始"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="2">
                                                        <Setter Property="Text" Value="进行中"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="3">
                                                        <Setter Property="Text" Value="已完成"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="4">
                                                        <Setter Property="Text" Value="已撤回"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="5">
                                                        <Setter Property="Text" Value="已取消"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 操作 -->
                    <DataGridTemplateColumn Header="操作" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <!-- 待排产状态显示排产按钮 -->
                                    <Button Content="排产" Margin="2" Padding="6,3" FontSize="11" 
                    Background="#28A745" Foreground="White" BorderThickness="0"
                    Command="{Binding DataContext.ScheduleCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                    CommandParameter="{Binding}"
                    Visibility="{Binding Status, Converter={StaticResource StatusToScheduleVisibilityConverter}}"/>

                                    <!-- 其他状态显示查看按钮 -->
                                    <Button Content="查看" Margin="2" Padding="6,3" FontSize="11" 
                    Background="#17A2B8" Foreground="White" BorderThickness="0"
                    Command="{Binding DataContext.ViewCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                    CommandParameter="{Binding}"
                    Visibility="{Binding Status, Converter={StaticResource StatusToViewVisibilityConverter}}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </ScrollViewer>
        <!-- 分页控件 -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15,10" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="共" VerticalAlignment="Center" Margin="5,0"/>
                <TextBlock Text="{Binding Total}" VerticalAlignment="Center" Margin="0,0,5,0" FontWeight="Bold" Foreground="#007BFF"/>
                <TextBlock Text="条记录" VerticalAlignment="Center" Margin="0,0,20,0"/>
                
                <Button Content="首页" Command="{Binding FirstPageCommand}" Margin="5" Padding="8,4" Background="#6C757D" Foreground="White" BorderThickness="0"/>
                <Button Content="上一页" Command="{Binding PrevPageCommand}" Margin="5" Padding="8,4" Background="#007BFF" Foreground="White" BorderThickness="0"/>
                
                <TextBlock Text="第" VerticalAlignment="Center" Margin="10,0,5,0"/>
                <TextBlock Text="{Binding Page}" VerticalAlignment="Center" Margin="0" FontWeight="Bold" Foreground="#007BFF"/>
                <TextBlock Text="页" VerticalAlignment="Center" Margin="5,0,10,0"/>
                
                <Button Content="下一页" Command="{Binding NextPageCommand}" Margin="5" Padding="8,4" Background="#007BFF" Foreground="White" BorderThickness="0"/>
                <Button Content="末页" Command="{Binding LastPageCommand}" Margin="5" Padding="8,4" Background="#6C757D" Foreground="White" BorderThickness="0"/>
                
                <TextBlock Text="每页显示" VerticalAlignment="Center" Margin="20,0,5,0"/>
                <ComboBox SelectedValue="{Binding PageSize}" Width="60" Margin="0,0,5,0">
                    <ComboBox.Items>
                        <sys:Int32>10</sys:Int32>
                        <sys:Int32>20</sys:Int32>
                        <sys:Int32>50</sys:Int32>
                        <sys:Int32>100</sys:Int32>
                    </ComboBox.Items>
                </ComboBox>
                <TextBlock Text="条" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
