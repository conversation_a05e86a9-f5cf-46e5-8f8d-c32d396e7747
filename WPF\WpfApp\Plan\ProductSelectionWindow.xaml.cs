using System.Windows;

namespace WpfApp.Plan
{
    /// <summary>
    /// ProductSelectionWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ProductSelectionWindow : Window
    {
        public ProductSelectionViewModel ViewModel { get; private set; }
        public ProductDisplayModel SelectedProduct { get; private set; }

        public ProductSelectionWindow()
        {
            InitializeComponent();
            ViewModel = new ProductSelectionViewModel();
            ViewModel.CloseWindow = (dialogResult, productModel) =>
            {
                System.Diagnostics.Debug.WriteLine($"=== ProductSelectionWindow CloseWindow 回调被调用 ===");
                System.Diagnostics.Debug.WriteLine($"dialogResult: {dialogResult}");
                System.Diagnostics.Debug.WriteLine($"productModel是否为null: {productModel == null}");

                if (dialogResult)
                {
                    SelectedProduct = productModel;
                    System.Diagnostics.Debug.WriteLine($"窗口关闭时设置选中产品: {productModel?.ProductName} - {productModel?.ProductCode} - ID: {productModel?.Id}");
                    System.Diagnostics.Debug.WriteLine($"SelectedProduct设置后: {SelectedProduct?.ProductName} - {SelectedProduct?.ProductCode} - ID: {SelectedProduct?.Id}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("用户取消了选择");
                }

                DialogResult = dialogResult;
                System.Diagnostics.Debug.WriteLine($"DialogResult设置为: {DialogResult}");
                Close();
            };
            DataContext = ViewModel;
        }

        protected override void OnClosed(System.EventArgs e)
        {
            ViewModel?.Dispose();
            base.OnClosed(e);
        }
    }
}
