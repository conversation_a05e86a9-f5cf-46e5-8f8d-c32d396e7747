using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using WpfApp.Common;

namespace WpfApp.ProcessRoute
{
    /// <summary>
    /// 新增工艺路线的数据处理类
    /// </summary>
    public class AddProcessRouteViewModel : INotifyPropertyChanged
    {
        /// <summary>
        /// 请求返回到工艺路线显示页面的事件
        /// </summary>
        public static event Action RequestReturnToProcessRouteDisplay;
        private readonly HttpClient httpClient;
        private string processRouteCode;
        private string processRouteName;
        private bool isSystemGenerated;
        private bool isEnabled = true;
        private bool isDisabled;
        private string description;
        private string remarks;
        private ObservableCollection<ProcessStepItem> processSteps;
        private ObservableCollection<ProductItem> relatedProducts;
        private ProcessStepItem selectedProcessStep;
        private ProductItem selectedProduct;

        // 工序组成相关属性
        private ObservableCollection<ProcessComposition> processCompositions;
        private ProcessComposition selectedProcessComposition;
        private string processRouteIdFilter;

        // 工艺路线列表相关属性
        private ObservableCollection<ProcessRouteItem> processRouteList;
        private ProcessRouteItem selectedProcessRoute;
        private string processRouteSearchId;

        public AddProcessRouteViewModel()
        {
            httpClient = new HttpClient();
            ProcessSteps = new ObservableCollection<ProcessStepItem>();
            RelatedProducts = new ObservableCollection<ProductItem>();
            ProcessCompositions = new ObservableCollection<ProcessComposition>();
            ProcessRouteList = new ObservableCollection<ProcessRouteItem>();

            // 添加测试数据以确保工序组成区域可见
            AddTestProcessCompositions();

            // 自动加载工艺路线数据
            _ = LoadProcessRouteListAsync();

            // 添加工艺路线管理测试数据
            AddTestProcessCompositionData();

            // 初始化命令
            AddProcessCommand = new RelayCommand(_ => AddProcess());
            EditProcessCommand = new RelayCommand(_ => EditProcess());
            DeleteProcessCommand = new RelayCommand(_ => DeleteProcess());
            AddProductCommand = new RelayCommand(_ => AddProduct());
            RemoveProductCommand = new RelayCommand(_ => RemoveProduct());
            SaveCommand = new RelayCommand(_ => SaveProcessRoute());
            CancelCommand = new RelayCommand(_ => Cancel());
            LoadDataCommand = new RelayCommand(async _ => await LoadDataAsync());
            SearchProcessRouteCommand = new RelayCommand(async _ => await SearchProcessRouteAsync());
            ClearSearchCommand = new RelayCommand(_ => ClearSearch());
            RefreshProcessRouteCommand = new RelayCommand(async _ => await RefreshProcessRouteAsync());
            AddNewProcessRouteCommand = new RelayCommand(_ => AddNewProcessRoute());
            EditProcessCompositionCommand = new RelayCommand(param => EditProcessComposition(param as ProcessComposition));
            DeleteProcessCompositionCommand = new RelayCommand(param => DeleteProcessComposition(param as ProcessComposition));
        }

        // 基础信息属性
        public string ProcessRouteCode
        {
            get => processRouteCode;
            set
            {
                processRouteCode = value;
                OnPropertyChanged(nameof(ProcessRouteCode));
            }
        }

        public string ProcessRouteName
        {
            get => processRouteName;
            set
            {
                processRouteName = value;
                OnPropertyChanged(nameof(ProcessRouteName));
            }
        }

        public bool IsSystemGenerated
        {
            get => isSystemGenerated;
            set
            {
                isSystemGenerated = value;
                OnPropertyChanged(nameof(IsSystemGenerated));
                if (value)
                {
                    ProcessRouteCode = GenerateSystemCode();
                }
            }
        }

        public bool IsEnabled
        {
            get => isEnabled;
            set
            {
                isEnabled = value;
                if (value) IsDisabled = false;
                OnPropertyChanged(nameof(IsEnabled));
            }
        }

        public bool IsDisabled
        {
            get => isDisabled;
            set
            {
                isDisabled = value;
                if (value) IsEnabled = false;
                OnPropertyChanged(nameof(IsDisabled));
            }
        }

        public string Description
        {
            get => description;
            set
            {
                description = value;
                OnPropertyChanged(nameof(Description));
            }
        }

        public string Remarks
        {
            get => remarks;
            set
            {
                remarks = value;
                OnPropertyChanged(nameof(Remarks));
            }
        }

        // 工序列表
        public ObservableCollection<ProcessStepItem> ProcessSteps
        {
            get => processSteps;
            set
            {
                processSteps = value;
                OnPropertyChanged(nameof(ProcessSteps));
            }
        }

        public ProcessStepItem SelectedProcessStep
        {
            get => selectedProcessStep;
            set
            {
                selectedProcessStep = value;
                OnPropertyChanged(nameof(SelectedProcessStep));
            }
        }

        // 关联产品列表
        public ObservableCollection<ProductItem> RelatedProducts
        {
            get => relatedProducts;
            set
            {
                relatedProducts = value;
                OnPropertyChanged(nameof(RelatedProducts));
            }
        }

        public ProductItem SelectedProduct
        {
            get => selectedProduct;
            set
            {
                selectedProduct = value;
                OnPropertyChanged(nameof(SelectedProduct));
            }
        }

        // 工序组成列表
        public ObservableCollection<ProcessComposition> ProcessCompositions
        {
            get => processCompositions;
            set
            {
                processCompositions = value;
                OnPropertyChanged(nameof(ProcessCompositions));
            }
        }

        public ProcessComposition SelectedProcessComposition
        {
            get => selectedProcessComposition;
            set
            {
                selectedProcessComposition = value;
                OnPropertyChanged(nameof(SelectedProcessComposition));
            }
        }

        public string ProcessRouteIdFilter
        {
            get => processRouteIdFilter;
            set
            {
                processRouteIdFilter = value;
                OnPropertyChanged(nameof(ProcessRouteIdFilter));
            }
        }

        // 工艺路线列表属性
        public ObservableCollection<ProcessRouteItem> ProcessRouteList
        {
            get => processRouteList;
            set
            {
                processRouteList = value;
                OnPropertyChanged(nameof(ProcessRouteList));
            }
        }

        public ProcessRouteItem SelectedProcessRoute
        {
            get => selectedProcessRoute;
            set
            {
                selectedProcessRoute = value;
                OnPropertyChanged(nameof(SelectedProcessRoute));
            }
        }

        public string ProcessRouteSearchId
        {
            get => processRouteSearchId;
            set
            {
                processRouteSearchId = value;
                OnPropertyChanged(nameof(ProcessRouteSearchId));
            }
        }

        // 命令
        public ICommand AddProcessCommand { get; }
        public ICommand EditProcessCommand { get; }
        public ICommand DeleteProcessCommand { get; }
        public ICommand AddProductCommand { get; }
        public ICommand RemoveProductCommand { get; }
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand LoadDataCommand { get; }
        public ICommand SearchProcessRouteCommand { get; }
        public ICommand ClearSearchCommand { get; }
        public ICommand RefreshProcessRouteCommand { get; }
        public ICommand AddNewProcessRouteCommand { get; }
        public ICommand EditProcessCompositionCommand { get; }
        public ICommand DeleteProcessCompositionCommand { get; }

        // 生成系统编号
        private static string GenerateSystemCode()
        {
            return $"PR{DateTime.Now:yyyyMMddHHmmss}";
        }

        // 添加工序
        private void AddProcess()
        {
            try
            {
                // 获取当前工艺路线ID，如果没有则生成一个
                string currentProcessRouteId = ProcessRouteCode ?? GenerateSystemCode();

                // 创建添加工序窗口，传入工艺路线ID和回调函数
                var addWindow = new AddProcessStepWindow(currentProcessRouteId, OnProcessAdded);
                addWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开添加工序窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 工序添加成功后的回调方法
        /// </summary>
        /// <param name="processStep">添加的工序项</param>
        private void OnProcessAdded(ProcessStepItem processStep)
        {
            if (processStep != null)
            {
                // 检查序号是否已存在
                if (ProcessSteps.Any(p => p.Sequence == processStep.Sequence))
                {
                    MessageBox.Show($"序号 {processStep.Sequence} 已存在，请使用其他序号", "序号重复",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 添加到工序列表
                ProcessSteps.Add(processStep);

                // 按序号排序
                var sortedList = ProcessSteps.OrderBy(p => p.Sequence).ToList();
                ProcessSteps.Clear();
                foreach (var item in sortedList)
                {
                    ProcessSteps.Add(item);
                }

                MessageBox.Show("工序添加成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        // 编辑工序
        private void EditProcess()
        {
            if (SelectedProcessStep != null)
            {
                MessageBox.Show($"编辑工序: {SelectedProcessStep.ProcessName}");
            }
            else
            {
                MessageBox.Show("请选择要编辑的工序");
            }
        }

        // 删除工序
        private void DeleteProcess()
        {
            if (SelectedProcessStep != null)
            {
                var result = MessageBox.Show($"确定删除工序 {SelectedProcessStep.ProcessName} 吗？",
                    "确认删除", MessageBoxButton.YesNo);
                if (result == MessageBoxResult.Yes)
                {
                    ProcessSteps.Remove(SelectedProcessStep);
                }
            }
            else
            {
                MessageBox.Show("请选择要删除的工序");
            }
        }

        // 添加产品
        private void AddProduct()
        {
            try
            {
                // 创建产品多选对话框
                var dialog = new ProductMultiSelectDialog();
                dialog.Owner = Application.Current.MainWindow;

                // 显示对话框
                var result = dialog.ShowDialog();

                if (result == true && dialog.SelectedProducts != null && dialog.SelectedProducts.Count > 0)
                {
                    // 获取当前最大序号
                    int maxSequence = RelatedProducts.Count > 0 ? RelatedProducts.Max(p => p.Sequence) : 0;

                    // 添加选中的产品到关联产品列表
                    foreach (var selectedProduct in dialog.SelectedProducts)
                    {
                        // 检查是否已经存在相同的产品
                        bool exists = RelatedProducts.Any(p => p.ProductCode == selectedProduct.ProductCode);
                        if (!exists)
                        {
                            maxSequence++;
                            RelatedProducts.Add(new ProductItem
                            {
                                Sequence = maxSequence,
                                ProductCode = selectedProduct.ProductCode,
                                ProductName = selectedProduct.ProductName,
                                Specification = selectedProduct.Specification,
                                Unit = selectedProduct.Unit,
                                BomCode = "", // 这些字段可能需要后续设置
                                BomVersion = ""
                            });
                        }
                    }

                    MessageBox.Show($"成功添加 {dialog.SelectedProducts.Count} 个产品", "成功",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加产品失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 移除产品
        private void RemoveProduct()
        {
            if (SelectedProduct != null)
            {
                var result = MessageBox.Show($"确定移除产品 {SelectedProduct.ProductName} 吗？",
                    "确认移除", MessageBoxButton.YesNo);
                if (result == MessageBoxResult.Yes)
                {
                    RelatedProducts.Remove(SelectedProduct);
                }
            }
            else
            {
                MessageBox.Show("请选择要移除的产品");
            }
        }

        // 保存工艺路线
        private async void SaveProcessRoute()
        {
            // 验证必填字段
            if (string.IsNullOrWhiteSpace(ProcessRouteCode))
            {
                MessageBox.Show("请输入工艺路线编号", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(ProcessRouteName))
            {
                MessageBox.Show("请输入工艺路线名称", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // 设置HTTP请求的认证头
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    httpClient.DefaultRequestHeaders.Clear();
                    httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                // 创建请求数据，根据后端接口要求
                var requestData = new
                {
                    processRouteCode = ProcessRouteCode,
                    processRouteName = ProcessRouteName,
                    isSystemGenerated = IsSystemGenerated,
                    isEnabled = IsEnabled,
                    description = Description ?? "",
                    remarks = Remarks ?? ""
                };

                // 序列化请求数据
                var jsonContent = System.Text.Json.JsonSerializer.Serialize(requestData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true // 格式化JSON以便调试
                });

                // 调试信息：显示请求数据
                System.Diagnostics.Debug.WriteLine($"保存工艺路线请求数据: {jsonContent}");

                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // 发送POST请求到工艺路线新增接口
                var response = await httpClient.PostAsync("http://localhost:5005/api/processRoute/processRoute", content);
                var responseString = await response.Content.ReadAsStringAsync();

                // 调试信息：显示响应数据
                System.Diagnostics.Debug.WriteLine($"保存工艺路线响应状态码: {response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"保存工艺路线响应内容: {responseString}");

                if (response.IsSuccessStatusCode)
                {
                    // 解析响应数据
                    var jsonOptions = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };

                    var apiResponse = System.Text.Json.JsonSerializer.Deserialize<ApiResponse>(responseString, jsonOptions);

                    if (apiResponse != null && apiResponse.Code == 200)
                    {
                        MessageBox.Show("工艺路线保存成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);

                        // 触发返回到工艺路线显示页面的事件
                        RequestReturnToProcessRouteDisplay?.Invoke();
                    }
                    else
                    {
                        string errorMessage = apiResponse?.Message ?? "保存失败，未知错误";
                        MessageBox.Show($"保存失败: {errorMessage}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"保存失败: HTTP {response.StatusCode}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存工艺路线异常: {ex}");
                MessageBox.Show($"保存失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 取消
        private void Cancel()
        {
            // 触发返回到工艺路线显示页面的事件
            RequestReturnToProcessRouteDisplay?.Invoke();
        }

        /// <summary>
        /// 异步加载工序组成数据的方法
        /// 从API获取工艺路线组成数据并更新UI
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                // 设置HTTP请求的认证头
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    httpClient.DefaultRequestHeaders.Clear();
                    httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                // 构建API请求URL
                var url = $"http://localhost:5005/api/processRoute/processCompositionList";
                if (!string.IsNullOrWhiteSpace(ProcessRouteIdFilter))
                {
                    url += $"?processRouteId={ProcessRouteIdFilter}";
                }

                // 发送HTTP GET请求
                var response = await httpClient.GetAsync(url);
                var jsonString = await response.Content.ReadAsStringAsync();

                // 调试输出
                System.Diagnostics.Debug.WriteLine($"API Response: {jsonString}");

                // 配置JSON反序列化选项
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    NumberHandling = JsonNumberHandling.AllowReadingFromString,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                };

                // 首先尝试使用简化模型进行反序列化
                try
                {
                    var simpleResult = JsonSerializer.Deserialize<ApiResult<System.Collections.Generic.List<ProcessCompositionSimple>>>(jsonString, jsonOptions);
                    if (simpleResult?.Result != null)
                    {
                        var convertedData = simpleResult.Result.Select(simple => new ProcessComposition
                        {
                            SerialCode = simple.SerialCodeDisplay?.Replace("@/ProcessRoute", "").Replace("@/Procedure", ""),
                            ProcessScriptId = int.TryParse(simple.ProcessScriptIdDisplay, out int pid) ? pid : 0,
                            NextProcedure = simple.NextProcedureDisplay,
                            Relationship = simple.RelationshipDisplay,
                            IsKeyProcess = simple.IsKeyProcessDisplay == "是",
                            DisplayIcon = simple.DisplayIconDisplay,
                            PreparationTime = simple.PreparationTimeDisplay,
                            WaitingTime = int.TryParse(simple.WaitingTimeDisplay, out int wt) ? wt : 0,
                            Remarks = simple.RemarksDisplay,
                            ProcessRouteId = simple.ProcessRouteIdDisplay,
                            IsDelete = simple.StatusDisplay == "已删除",
                            CreateTime = DateTime.TryParse(simple.CreateTimeDisplay, out DateTime ct) ? ct : DateTime.Now,
                            CreatorUserId = simple.CreatorUserIdDisplay,
                            CreatorUserName = simple.CreatorUserNameDisplay,
                            UpdateUserName = simple.UpdateUserNameDisplay,
                            Id = simple.IdDisplay
                        }).ToList();

                        ProcessCompositions = new ObservableCollection<ProcessComposition>(convertedData);
                        return;
                    }
                }
                catch (Exception simpleEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Simple model failed: {simpleEx.Message}");
                }

                // 如果简化模型失败，尝试使用原始模型
                var apiResult = JsonSerializer.Deserialize<ApiResult<System.Collections.Generic.List<ProcessComposition>>>(jsonString, jsonOptions);
                if (apiResult?.Result != null)
                {
                    ProcessCompositions = new ObservableCollection<ProcessComposition>(apiResult.Result);
                }
                else
                {
                    ProcessCompositions.Clear();
                }
            }
            catch (JsonException jsonEx)
            {
                MessageBox.Show($"JSON解析失败: {jsonEx.Message}\n\n请检查API返回的数据格式", "JSON错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"JSON Exception: {jsonEx}");
            }
            catch (HttpRequestException httpEx)
            {
                MessageBox.Show($"网络请求失败: {httpEx.Message}", "网络错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据失败: {ex.Message}\n\n详细信息: {ex}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"General Exception: {ex}");
            }
        }

        /// <summary>
        /// 加载工艺路线列表
        /// </summary>
        private async Task LoadProcessRouteListAsync()
        {
            try
            {
                // 设置HTTP请求的认证头
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    httpClient.DefaultRequestHeaders.Clear();
                    httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                // 构建API请求URL
                var url = "http://localhost:5005/api/processRoute/processRouteList";

                // 发送HTTP GET请求
                var response = await httpClient.GetAsync(url);
                var jsonString = await response.Content.ReadAsStringAsync();

                // 调试输出
                System.Diagnostics.Debug.WriteLine($"Load Process Route API Response: {jsonString}");

                // 配置JSON反序列化选项
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    NumberHandling = JsonNumberHandling.AllowReadingFromString,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                };

                // 反序列化API响应
                var apiResult = JsonSerializer.Deserialize<ApiResult<System.Collections.Generic.List<ProcessRouteItem>>>(jsonString, jsonOptions);
                if (apiResult?.Code == 200 && apiResult.Result != null)
                {
                    ProcessRouteList = new ObservableCollection<ProcessRouteItem>(apiResult.Result);
                }
                else
                {
                    ProcessRouteList.Clear();
                    System.Diagnostics.Debug.WriteLine($"API返回错误: {apiResult?.Message ?? "未知错误"}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载工艺路线列表失败: {ex.Message}");
                // 不显示错误消息框，避免在初始化时打扰用户
            }
        }

        /// <summary>
        /// 搜索工艺路线
        /// </summary>
        private async Task SearchProcessRouteAsync()
        {
            try
            {
                // 设置HTTP请求的认证头
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    httpClient.DefaultRequestHeaders.Clear();
                    httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                // 构建API请求URL - 使用正确的API端点
                var url = "http://localhost:5005/api/processRoute/processRouteList";
                if (!string.IsNullOrWhiteSpace(ProcessRouteSearchId))
                {
                    url += $"?ProcessRouteId={ProcessRouteSearchId}";
                }

                // 发送HTTP GET请求
                var response = await httpClient.GetAsync(url);
                var jsonString = await response.Content.ReadAsStringAsync();

                // 调试输出
                System.Diagnostics.Debug.WriteLine($"Process Route API Response: {jsonString}");

                // 配置JSON反序列化选项
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    NumberHandling = JsonNumberHandling.AllowReadingFromString,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                };

                // 反序列化API响应
                var apiResult = JsonSerializer.Deserialize<ApiResult<System.Collections.Generic.List<ProcessRouteItem>>>(jsonString, jsonOptions);
                if (apiResult?.Result != null)
                {
                    ProcessRouteList = new ObservableCollection<ProcessRouteItem>(apiResult.Result);
                }
                else
                {
                    ProcessRouteList.Clear();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索工艺路线失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"Search Process Route Exception: {ex}");
            }
        }

        /// <summary>
        /// 清空搜索条件
        /// </summary>
        private void ClearSearch()
        {
            ProcessRouteSearchId = string.Empty;
            ProcessRouteList.Clear();
        }

        /// <summary>
        /// 刷新工艺路线列表
        /// </summary>
        private async Task RefreshProcessRouteAsync()
        {
            ProcessRouteSearchId = string.Empty;
            await SearchProcessRouteAsync();
        }

        /// <summary>
        /// 新增工艺路线
        /// </summary>
        private void AddNewProcessRoute()
        {
            try
            {
                // 这里可以打开新增工艺路线的窗口或执行相关逻辑
                MessageBox.Show("新增工艺路线功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新增工艺路线失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 添加测试工序组成数据
        /// </summary>
        private void AddTestProcessCompositions()
        {
            try
            {
                ProcessCompositions.Add(new ProcessComposition
                {
                    SerialCode = "001",
                    ProcessScriptId = 1001,
                    NextProcedure = "002",
                    Relationship = "顺序",
                    IsKeyProcess = true,
                    PreparationTime = "10分钟",
                    WaitingTime = 5,
                    Remarks = "测试工序组成1",
                    ProcessRouteId = "TEST001",
                    CreateTime = DateTime.Now
                });

                ProcessCompositions.Add(new ProcessComposition
                {
                    SerialCode = "002",
                    ProcessScriptId = 1002,
                    NextProcedure = "003",
                    Relationship = "顺序",
                    IsKeyProcess = false,
                    PreparationTime = "15分钟",
                    WaitingTime = 3,
                    Remarks = "测试工序组成2",
                    ProcessRouteId = "TEST001",
                    CreateTime = DateTime.Now
                });

                ProcessCompositions.Add(new ProcessComposition
                {
                    SerialCode = "003",
                    ProcessScriptId = 1003,
                    NextProcedure = "",
                    Relationship = "结束",
                    IsKeyProcess = true,
                    PreparationTime = "20分钟",
                    WaitingTime = 0,
                    Remarks = "测试工序组成3",
                    ProcessRouteId = "TEST001",
                    CreateTime = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加测试数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加测试工艺路线列表数据
        /// </summary>
        private void AddTestProcessRouteList()
        {
            try
            {
                ProcessRouteList.Add(new ProcessRouteItem
                {
                    Sequence = "sttr",
                    ProcessScriptId = 0,
                    NextProcess = "3",
                    ProcessRouteId = "GY500001",
                    Status = "已完成",
                    Remarks = "string",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "系统管理员",
                    Id = "1"
                });

                ProcessRouteList.Add(new ProcessRouteItem
                {
                    Sequence = "sttr",
                    ProcessScriptId = 0,
                    NextProcess = "0",
                    ProcessRouteId = "GY500001",
                    Status = "进行中",
                    Remarks = "string",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "系统管理员",
                    Id = "2"
                });

                ProcessRouteList.Add(new ProcessRouteItem
                {
                    Sequence = "1",
                    ProcessScriptId = 0,
                    NextProcess = "0",
                    ProcessRouteId = "PR20250728102274",
                    Status = "进行中",
                    Remarks = "制作工序",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "操作员",
                    Id = "3"
                });

                ProcessRouteList.Add(new ProcessRouteItem
                {
                    Sequence = "1",
                    ProcessScriptId = 0,
                    NextProcess = "0",
                    ProcessRouteId = "PR20250728103040",
                    Status = "已完成",
                    Remarks = "制作工序",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "操作员",
                    Id = "4"
                });

                ProcessRouteList.Add(new ProcessRouteItem
                {
                    Sequence = "1",
                    ProcessScriptId = 0,
                    NextProcess = "0",
                    ProcessRouteId = "PR20250728103246",
                    Status = "已完成",
                    Remarks = "制作工序",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "操作员",
                    Id = "5"
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加工艺路线列表测试数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加测试工艺路线组成数据
        /// </summary>
        private void AddTestProcessCompositionData()
        {
            try
            {
                ProcessCompositions.Add(new ProcessComposition
                {
                    SerialCode = "sttr",
                    ProcessScriptId = 0,
                    NextProcedure = "3",
                    ProcessRouteId = "GYbh0001",
                    Remarks = "string",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "系统管理员",
                    Id = "1",
                    IsDelete = false,
                    IsKeyProcess = false,
                    DisplayIcon = "icon1",
                    PreparationTime = "10",
                    WaitingTime = 5,
                    Relationship = "串行",
                    CreatorUserId = "admin",
                    UpdateUserName = "系统管理员"
                });

                ProcessCompositions.Add(new ProcessComposition
                {
                    SerialCode = "sttr",
                    ProcessScriptId = 0,
                    NextProcedure = "0",
                    ProcessRouteId = "GYbh0001",
                    Remarks = "string",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "系统管理员",
                    Id = "2",
                    IsDelete = false,
                    IsKeyProcess = false,
                    DisplayIcon = "icon2",
                    PreparationTime = "15",
                    WaitingTime = 3,
                    Relationship = "串行",
                    CreatorUserId = "admin",
                    UpdateUserName = "系统管理员"
                });

                ProcessCompositions.Add(new ProcessComposition
                {
                    SerialCode = "1",
                    ProcessScriptId = 0,
                    NextProcedure = "0",
                    ProcessRouteId = "PR20250728102720",
                    Remarks = "测试工序",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "操作员",
                    Id = "3",
                    IsDelete = false,
                    IsKeyProcess = true,
                    DisplayIcon = "icon3",
                    PreparationTime = "20",
                    WaitingTime = 8,
                    Relationship = "并行",
                    CreatorUserId = "operator",
                    UpdateUserName = "操作员"
                });

                ProcessCompositions.Add(new ProcessComposition
                {
                    SerialCode = "1",
                    ProcessScriptId = 0,
                    NextProcedure = "0",
                    ProcessRouteId = "PR20250728103400",
                    Remarks = "测试工序",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "操作员",
                    Id = "4",
                    IsDelete = false,
                    IsKeyProcess = false,
                    DisplayIcon = "icon4",
                    PreparationTime = "12",
                    WaitingTime = 6,
                    Relationship = "串行",
                    CreatorUserId = "operator",
                    UpdateUserName = "操作员"
                });

                ProcessCompositions.Add(new ProcessComposition
                {
                    SerialCode = "1",
                    ProcessScriptId = 0,
                    NextProcedure = "0",
                    ProcessRouteId = "PR20250728103440",
                    Remarks = "测试工序",
                    CreateTime = DateTime.Now,
                    CreatorUserName = "操作员",
                    Id = "5",
                    IsDelete = false,
                    IsKeyProcess = false,
                    DisplayIcon = "icon5",
                    PreparationTime = "18",
                    WaitingTime = 4,
                    Relationship = "串行",
                    CreatorUserId = "operator",
                    UpdateUserName = "操作员"
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加工艺路线组成测试数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 编辑工艺路线组成
        /// </summary>
        private void EditProcessComposition(ProcessComposition composition)
        {
            if (composition != null)
            {
                // 这里可以打开编辑窗口或执行编辑逻辑
                System.Diagnostics.Debug.WriteLine($"编辑工艺路线组成: {composition.ProcessRouteId}");
            }
        }

        /// <summary>
        /// 删除工艺路线组成
        /// </summary>
        private void DeleteProcessComposition(ProcessComposition composition)
        {
            if (composition != null)
            {
                // 这里可以执行删除逻辑
                ProcessCompositions.Remove(composition);
                System.Diagnostics.Debug.WriteLine($"删除工艺路线组成: {composition.ProcessRouteId}");
            }
        }

        // 属性变化通知
        public event PropertyChangedEventHandler PropertyChanged;

        private void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 工序项目数据模型
    /// </summary>
    public class ProcessStepItem
    {
        public int Sequence { get; set; }
        public string ProcessCode { get; set; }
        public string ProcessName { get; set; }
        public string NextProcess { get; set; }
        public string Relationship { get; set; }
        public string IsKeyProcess { get; set; }
        public string PrepareTime { get; set; }
        public string WaitTime { get; set; }
    }

    /// <summary>
    /// 产品项目数据模型
    /// </summary>
    public class ProductItem
    {
        public int Sequence { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string Specification { get; set; }
        public string Unit { get; set; }
        public string BomCode { get; set; }
        public string BomVersion { get; set; }
    }

    /// <summary>
    /// API响应基础类
    /// </summary>
    public class ApiResponse
    {
        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("result")]
        public object Result { get; set; }
    }
}


