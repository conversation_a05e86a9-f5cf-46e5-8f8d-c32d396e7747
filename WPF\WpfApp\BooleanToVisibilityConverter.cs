using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace WpfApp
{
    /// <summary>
    /// 将布尔值转换为可见性的值转换器
    /// 支持通过参数进行反向转换
    /// </summary>
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool boolValue = value is bool b && b;

            // 如果参数是"Invert"，则反向转换
            if (parameter?.ToString() == "Invert")
            {
                boolValue = !boolValue;
            }

            return boolValue ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool result = value is Visibility visibility && visibility == Visibility.Visible;

            // 如果参数是"Invert"，则反向转换
            if (parameter?.ToString() == "Invert")
            {
                result = !result;
            }

            return result;
        }
    }
} 