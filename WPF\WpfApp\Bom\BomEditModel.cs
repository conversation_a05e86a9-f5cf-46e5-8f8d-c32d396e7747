using System.Collections.Generic;
using System.ComponentModel;

/// <summary>
/// BOM编辑模型（用于新增和编辑操作，包含所有字段）1231
/// </summary>
public class BomEditModel : INotifyPropertyChanged
{
    /// <summary>
    /// BOM 编号
    /// </summary>
    public string BomCode { get; set; }

    /// <summary>
    /// 是否系统编号（开关状态）
    /// </summary>
    public bool IsSystemCode { get; set; }

    /// <summary>
    /// 是否默认 BOM
    /// </summary>
    public bool IsDefaultBom { get; set; }

    /// <summary>
    /// BOM 版本
    /// </summary>
    public string BomVersion { get; set; }

    /// <summary>
    /// 产品id（必填，关联物料）
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// 产品编号
    /// </summary>
    public string ProductCode { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string Specification { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 日产量
    /// </summary>
    public int DailyOutput { get; set; }

    /// <summary>
    /// 备注信息
    /// </summary>
    public string Remarks { get; set; }

    /// <summary>
    /// 工艺路线Id
    /// </summary>
    public string ProcessRouteId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int State { get; set; }

    /// <summary>
    /// 物料IDs
    /// </summary>
    public List<int> MaterialIDs { get; set; }

    /// <summary>
    /// 工序主键Id
    /// </summary>
    public int ProcessStepId { get; set; }

    /// <summary>
    /// 物料Ids
    /// </summary>
    public List<int> MaterialIdsw { get; set; }

    public BomEditModel()
    {
        MaterialIDs = new List<int>();
        MaterialIdsw = new List<int>();
        BomCode = string.Empty;
        BomVersion = string.Empty;
        ProductCode = string.Empty;
        Specification = string.Empty;
        Unit = string.Empty;
        Remarks = string.Empty;
        ProcessRouteId = string.Empty;
    }

    /// <summary>
    /// 从显示模型转换为编辑模型
    /// </summary>
    public static BomEditModel FromBomProduct(BomProduct product)
    {
        return new BomEditModel
        {
            BomCode = product.BomCode,
            IsSystemCode = product.IsSystemCode,
            IsDefaultBom = product.IsDefaultBom,
            BomVersion = product.BomVersion,
            ProductId = product.ProductId,
            ProductCode = product.ProductCode,
            Specification = product.Specification,
            Unit = product.Unit,
            DailyOutput = product.DailyOutput,
            Remarks = product.Remarks,
            ProcessRouteId = product.ProcessRouteId
        };
    }

    /// <summary>
    /// 转换为显示模型
    /// </summary>
    public BomProduct ToBomProduct()
    {
        return new BomProduct
        {
            BomCode = BomCode,
            IsSystemCode = IsSystemCode,
            IsDefaultBom = IsDefaultBom,
            BomVersion = BomVersion,
            ProductId = ProductId,
            ProductCode = ProductCode,
            Specification = Specification,
            Unit = Unit,
            DailyOutput = DailyOutput,
            Remarks = Remarks,
            ProcessRouteId = ProcessRouteId
        };
    }

    public event PropertyChangedEventHandler PropertyChanged;
    protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
}
