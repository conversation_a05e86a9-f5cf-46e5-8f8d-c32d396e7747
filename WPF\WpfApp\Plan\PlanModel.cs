using System;
using System.ComponentModel;
using System.Globalization;
using System.Windows.Data;
using System.Windows;

namespace WpfApp.Plan
{
    /// <summary>
    /// 生产计划模型
    /// </summary>
    public class PlanModel : INotifyPropertyChanged
    {
        private bool _isSelected;
        private bool _canDecompose;
        private bool _canWithdraw;
        private bool _canEdit;

        // 添加选择状态改变事件
        public event Action<PlanModel> IsSelectedChanged;
        
        private string _id;
        private string _planCode;
        private string _planName;
        private int _workOrderNumber;
        private string _sourceId;
        private string _sourceName;
        private string _productId;
        private string _productName;
        private string _productCode;
        private string _specification;
        private string _productSpecification;
        private string _unit;
        private string _productUnit;
        private int _planNumber;
        private DateTime? _planStartTime;
        private DateTime? _planEndTime;
        private DateTime? _demandTime;
        private int _planStatus;
        private string _planStatusName;
        private string _planRemark;
        private string _orderId;
        private string _productType;
        private string _planAttachment;
        private string _bomId;
        private string _bomCode;
        private string _bomVersion;

        public string Id 
        { 
            get => _id; 
            set 
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }
        
        public string PlanCode 
        { 
            get => _planCode; 
            set 
            {
                if (_planCode != value)
                {
                    _planCode = value;
                    OnPropertyChanged(nameof(PlanCode));
                }
            }
        }
        
        public string PlanName 
        { 
            get => _planName; 
            set 
            {
                if (_planName != value)
                {
                    _planName = value;
                    OnPropertyChanged(nameof(PlanName));
                }
            }
        }
        
        public int WorkOrderNumber 
        { 
            get => _workOrderNumber; 
            set 
            {
                if (_workOrderNumber != value)
                {
                    _workOrderNumber = value;
                    OnPropertyChanged(nameof(WorkOrderNumber));
                }
            }
        }
        
        public string SourceId 
        { 
            get => _sourceId; 
            set 
            {
                if (_sourceId != value)
                {
                    _sourceId = value;
                    OnPropertyChanged(nameof(SourceId));
                }
            }
        }
        
        public string SourceName 
        { 
            get => _sourceName; 
            set 
            {
                if (_sourceName != value)
                {
                    _sourceName = value;
                    OnPropertyChanged(nameof(SourceName));
                }
            }
        }
        
        public string ProductId 
        { 
            get => _productId; 
            set 
            {
                if (_productId != value)
                {
                    _productId = value;
                    OnPropertyChanged(nameof(ProductId));
                }
            }
        }
        
        public string ProductName 
        { 
            get => _productName; 
            set 
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged(nameof(ProductName));
                }
            }
        }
        
        public string ProductCode 
        { 
            get => _productCode; 
            set 
            {
                if (_productCode != value)
                {
                    _productCode = value;
                    OnPropertyChanged(nameof(ProductCode));
                }
            }
        }
        
        public string Specification
        {
            get => _specification;
            set
            {
                if (_specification != value)
                {
                    _specification = value;
                    OnPropertyChanged(nameof(Specification));
                }
            }
        }

        public string ProductSpecification
        {
            get => _productSpecification;
            set
            {
                if (_productSpecification != value)
                {
                    _productSpecification = value;
                    OnPropertyChanged(nameof(ProductSpecification));
                }
            }
        }

        public string Unit
        {
            get => _unit;
            set
            {
                if (_unit != value)
                {
                    _unit = value;
                    OnPropertyChanged(nameof(Unit));
                }
            }
        }

        public string ProductUnit
        {
            get => _productUnit;
            set
            {
                if (_productUnit != value)
                {
                    _productUnit = value;
                    OnPropertyChanged(nameof(ProductUnit));
                }
            }
        }
        
        public int PlanNumber 
        { 
            get => _planNumber; 
            set 
            {
                if (_planNumber != value)
                {
                    _planNumber = value;
                    OnPropertyChanged(nameof(PlanNumber));
                }
            }
        }
        
        public DateTime? PlanStartTime 
        { 
            get => _planStartTime; 
            set 
            {
                if (_planStartTime != value)
                {
                    _planStartTime = value;
                    OnPropertyChanged(nameof(PlanStartTime));
                }
            }
        }
        
        public DateTime? PlanEndTime 
        { 
            get => _planEndTime; 
            set 
            {
                if (_planEndTime != value)
                {
                    _planEndTime = value;
                    OnPropertyChanged(nameof(PlanEndTime));
                }
            }
        }
        
        public DateTime? DemandTime 
        { 
            get => _demandTime; 
            set 
            {
                if (_demandTime != value)
                {
                    _demandTime = value;
                    OnPropertyChanged(nameof(DemandTime));
                }
            }
        }
        
        public int PlanStatus 
        { 
            get => _planStatus; 
            set 
            {
                if (_planStatus != value)
                {
                    _planStatus = value;
                    OnPropertyChanged(nameof(PlanStatus));
                }
            }
        }
        
        public string PlanStatusName 
        { 
            get => _planStatusName; 
            set 
            {
                if (_planStatusName != value)
                {
                    _planStatusName = value;
                    OnPropertyChanged(nameof(PlanStatusName));
                }
            }
        }
        
        public string PlanRemark 
        { 
            get => _planRemark; 
            set 
            {
                if (_planRemark != value)
                {
                    _planRemark = value;
                    OnPropertyChanged(nameof(PlanRemark));
                }
            }
        }
        
        public string OrderId 
        { 
            get => _orderId; 
            set 
            {
                if (_orderId != value)
                {
                    _orderId = value;
                    OnPropertyChanged(nameof(OrderId));
                }
            }
        }
        
        public string ProductType 
        { 
            get => _productType; 
            set 
            {
                if (_productType != value)
                {
                    _productType = value;
                    OnPropertyChanged(nameof(ProductType));
                }
            }
        }
        
        public string PlanAttachment 
        { 
            get => _planAttachment; 
            set 
            {
                if (_planAttachment != value)
                {
                    _planAttachment = value;
                    OnPropertyChanged(nameof(PlanAttachment));
                }
            }
        }
        
        public string BomId 
        { 
            get => _bomId; 
            set 
            {
                if (_bomId != value)
                {
                    _bomId = value;
                    OnPropertyChanged(nameof(BomId));
                }
            }
        }
        
        public string BomCode 
        { 
            get => _bomCode; 
            set 
            {
                if (_bomCode != value)
                {
                    _bomCode = value;
                    OnPropertyChanged(nameof(BomCode));
                }
            }
        }
        
        public string BomVersion 
        { 
            get => _bomVersion; 
            set 
            {
                if (_bomVersion != value)
                {
                    _bomVersion = value;
                    OnPropertyChanged(nameof(BomVersion));
                }
            }
        }
        
        // 用于UI的属性
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                    // 通知选择状态改变
                    IsSelectedChanged?.Invoke(this);
                }
            }
        }
        
        public bool CanDecompose
        {
            get => _canDecompose;
            set
            {
                if (_canDecompose != value)
                {
                    _canDecompose = value;
                    OnPropertyChanged(nameof(CanDecompose));
                }
            }
        }
        
        public bool CanWithdraw
        {
            get => _canWithdraw;
            set
            {
                if (_canWithdraw != value)
                {
                    _canWithdraw = value;
                    OnPropertyChanged(nameof(CanWithdraw));
                }
            }
        }
        
        public bool CanEdit
        {
            get => _canEdit;
            set
            {
                if (_canEdit != value)
                {
                    _canEdit = value;
                    OnPropertyChanged(nameof(CanEdit));
                }
            }
        }
        
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
}

/// <summary>
/// API响应类
/// </summary>
public class PlanPageResponse
{
    public int Code { get; set; }
    public string Type { get; set; }
    public string Message { get; set; }
    public PlanPageResult Result { get; set; }
}

/// <summary>
/// 分页结果
/// </summary>
public class PlanPageResult
{
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int Total { get; set; }
    public int TotalPages { get; set; }
    public System.Collections.Generic.List<WpfApp.Plan.PlanModel> Items { get; set; }
    public bool HasPrevPage { get; set; }
    public bool HasNextPage { get; set; }
} 

    public class PageSizeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }
    
    // 字符串到可见性转换器
    public class StringToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return string.IsNullOrEmpty(value as string) ? Visibility.Collapsed : Visibility.Visible;
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    
    // 文件上传响应模型
    public class FileUploadResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public FileUploadResult Result { get; set; }
        public object Extras { get; set; }
        public string Time { get; set; }
    }
    
    public class FileUploadResult
    {
        public string Provider { get; set; }
        public string BucketName { get; set; }
        public string FileName { get; set; }
        public string Suffix { get; set; }
        public string FilePath { get; set; }
        public string SizeKb { get; set; }
        public object SizeInfo { get; set; }
        public string Url { get; set; }
        public string FileMd5 { get; set; }
        public string FileType { get; set; }
        public object FileAlias { get; set; }
        public bool IsPublic { get; set; }
        public object DataId { get; set; }
        public string TenantId { get; set; }
        public string OrgId { get; set; }
        public string CreateTime { get; set; }
        public object UpdateTime { get; set; }
        public string CreateUserId { get; set; }
        public string CreateUserName { get; set; }
        public object UpdateUserId { get; set; }
        public object UpdateUserName { get; set; }
        public string Id { get; set; }
    } 