using System.Windows;

namespace WpfApp.Plan
{
    /// <summary>
    /// BomSelectionWindow.xaml 的交互逻辑
    /// </summary>
    public partial class BomSelectionWindow : Window
    {
        public BomSelectionViewModel ViewModel { get; private set; }
        public BomModel SelectedBom { get; private set; }

        public BomSelectionWindow(string productId, string productName, string productCode, string specification, string unit)
        {
            InitializeComponent();
            ViewModel = new BomSelectionViewModel(productId, productName, productCode, specification, unit);
            ViewModel.CloseWindow = (dialogResult, bomModel) =>
            {
                if (dialogResult)
                {
                    SelectedBom = bomModel;
                }
                DialogResult = dialogResult;
                Close();
            };
            DataContext = ViewModel;
        }
    }
} 