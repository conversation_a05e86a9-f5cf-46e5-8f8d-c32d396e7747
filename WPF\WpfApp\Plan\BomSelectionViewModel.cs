using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace WpfApp.Plan
{
    public class BomSelectionViewModel : INotifyPropertyChanged
    {
        private readonly string _productId;
        private readonly HttpClient _httpClient;
        
        private ObservableCollection<BomDisplayModel> _boms = new ObservableCollection<BomDisplayModel>();
        private bool _isLoading;
        private string _searchKeyword = "";
        private BomDisplayModel _selectedBom;
        private int _currentPage = 1;
        private int _pageSize = 5;
        private int _totalCount;
        private int _totalPages;
        private int _goToPage;
        
        // 产品信息
        private string _productName;
        private string _productCode;
        private string _specification;
        private string _unit;
        
        public ObservableCollection<BomDisplayModel> Boms
        {
            get => _boms;
            set
            {
                _boms = value;
                OnPropertyChanged(nameof(Boms));
                OnPropertyChanged(nameof(IsEmpty));
            }
        }
        
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
            }
        }
        
        public string SearchKeyword
        {
            get => _searchKeyword;
            set
            {
                _searchKeyword = value;
                OnPropertyChanged(nameof(SearchKeyword));
            }
        }
        
        public BomDisplayModel SelectedBom
        {
            get => _selectedBom;
            set
            {
                _selectedBom = value;
                OnPropertyChanged(nameof(SelectedBom));
            }
        }
        
        public bool IsEmpty => Boms.Count == 0 && !IsLoading;
        
        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                if (value < 1) value = 1;
                if (value > TotalPages && TotalPages > 0) value = TotalPages;
                
                if (_currentPage != value)
                {
                    _currentPage = value;
                    OnPropertyChanged(nameof(CurrentPage));
                    LoadData();
                }
            }
        }
        
        public int GoToPage
        {
            get => _goToPage;
            set
            {
                _goToPage = value;
                OnPropertyChanged(nameof(GoToPage));
                if (value >= 1 && value <= TotalPages)
                {
                    CurrentPage = value;
                }
            }
        }
        
        public int PageSize
        {
            get => _pageSize;
            set
            {
                if (_pageSize != value)
                {
                    _pageSize = value;
                    OnPropertyChanged(nameof(PageSize));
                    CurrentPage = 1; // 重置到第一页
                    LoadData();
                }
            }
        }
        
        public int TotalCount
        {
            get => _totalCount;
            set
            {
                _totalCount = value;
                OnPropertyChanged(nameof(TotalCount));
                OnPropertyChanged(nameof(TotalCountText));
            }
        }
        
        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged(nameof(TotalPages));
            }
        }
        
        public string TotalCountText => $"共 {TotalCount} 条";
        
        // 产品信息属性
        public string ProductName
        {
            get => _productName;
            set
            {
                _productName = value;
                OnPropertyChanged(nameof(ProductName));
            }
        }
        
        public string ProductCode
        {
            get => _productCode;
            set
            {
                _productCode = value;
                OnPropertyChanged(nameof(ProductCode));
            }
        }
        
        public string Specification
        {
            get => _specification;
            set
            {
                _specification = value;
                OnPropertyChanged(nameof(Specification));
            }
        }
        
        public string Unit
        {
            get => _unit;
            set
            {
                _unit = value;
                OnPropertyChanged(nameof(Unit));
            }
        }
        
        public ICommand SearchCommand { get; }
        public ICommand ConfirmCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand PrevPageCommand { get; }
        public ICommand NextPageCommand { get; }
        
        public Action<bool, BomModel> CloseWindow { get; set; }
        
        public BomSelectionViewModel(string productId, string productName, string productCode, string specification, string unit)
        {
            _productId = productId;
            ProductName = productName ?? "未知产品";
            ProductCode = productCode ?? "未知编号";
            Specification = specification ?? "未知规格";
            Unit = unit ?? "个";
            
            System.Diagnostics.Debug.WriteLine($"BomSelectionViewModel initialized with:");
            System.Diagnostics.Debug.WriteLine($"  ProductId: {productId}");
            System.Diagnostics.Debug.WriteLine($"  ProductName: {ProductName}");
            System.Diagnostics.Debug.WriteLine($"  ProductCode: {ProductCode}");
            System.Diagnostics.Debug.WriteLine($"  Specification: {Specification}");
            System.Diagnostics.Debug.WriteLine($"  Unit: {Unit}");
            
            // 创建HttpClient并设置基地址
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri("http://localhost:5005/");
            _httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

            // 添加Authorization头
            if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
            }
            
            // 初始化命令
            SearchCommand = new RelayCommand(() => Search());
            ConfirmCommand = new RelayCommand(() => Confirm(), () => CanConfirm());
            CancelCommand = new RelayCommand(() => Cancel());
            PrevPageCommand = new RelayCommand(() => PrevPage(), () => CanPrevPage());
            NextPageCommand = new RelayCommand(() => NextPage(), () => CanNextPage());
            
            // 加载BOM数据
            LoadData();
        }
        
        private async void LoadData()
        {
            IsLoading = true;
            Boms.Clear();
            
            try
            {
                // 简化过滤器，只使用最基本的参数
                var url = $"api/productionPlan/pageBom?Page={CurrentPage}&PageSize={PageSize}";
                
                System.Diagnostics.Debug.WriteLine($"Request URL: {url}");
                
                // 发起API请求
                var response = await _httpClient.GetAsync(url);
                var content = await response.Content.ReadAsStringAsync();
                
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"API Response Status: {response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"API Response Content: {content}");
                
                if (response.IsSuccessStatusCode)
                {
                    var apiResponse = System.Text.Json.JsonSerializer.Deserialize<ApiResponse<PagedResult<BomModel>>>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        AllowTrailingCommas = true,
                        ReadCommentHandling = System.Text.Json.JsonCommentHandling.Skip
                    });
                    
                    System.Diagnostics.Debug.WriteLine($"API Response Code: {apiResponse?.Code}");
                    System.Diagnostics.Debug.WriteLine($"API Response Type: {apiResponse?.Type}");
                    System.Diagnostics.Debug.WriteLine($"API Response Result: {apiResponse?.Result != null}");
                    
                    if ((apiResponse?.Type == "success" || apiResponse?.Code == 200) && apiResponse.Result != null)
                    {
                        // 设置分页信息，即使没有数据也要更新
                        TotalCount = apiResponse.Result.Total;
                        TotalPages = apiResponse.Result.TotalPages;
                        
                        if (apiResponse.Result.Items != null && apiResponse.Result.Items.Length > 0)
                        {
                            int rowNumber = (CurrentPage - 1) * PageSize + 1;
                            foreach (var bom in apiResponse.Result.Items)
                            {
                                Boms.Add(new BomDisplayModel
                                {
                                    RowNumber = rowNumber++,
                                    Model = bom
                                });
                            }
                            
                            System.Diagnostics.Debug.WriteLine($"Added {Boms.Count} BOM items to display");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"API returned success but had no items");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"API response was not successful or had invalid format");
                        MessageBox.Show("无法获取BOM数据，请联系系统管理员", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"API request failed: {response.StatusCode} - {content}");
                    MessageBox.Show($"服务器请求失败: {response.StatusCode}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Exception in LoadData: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Exception Stack Trace: {ex.StackTrace}");
                MessageBox.Show($"发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                OnPropertyChanged(nameof(IsEmpty));
            }
        }

        private async void LoadDataWithSearch()
        {
            IsLoading = true;
            Boms.Clear();
            
            try
            {
                // 简化过滤器，只使用基本参数和搜索关键词
                var url = $"api/productionPlan/pageBom?Page={CurrentPage}&PageSize={PageSize}&keyword={SearchKeyword}";
                
                System.Diagnostics.Debug.WriteLine($"Search Request URL: {url}");
                
                // 发起API请求
                var response = await _httpClient.GetAsync(url);
                var content = await response.Content.ReadAsStringAsync();
                
                System.Diagnostics.Debug.WriteLine($"Search API Response Status: {response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"Search API Response Content: {content}");
                
                if (response.IsSuccessStatusCode)
                {
                    var apiResponse = System.Text.Json.JsonSerializer.Deserialize<ApiResponse<PagedResult<BomModel>>>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        AllowTrailingCommas = true,
                        ReadCommentHandling = System.Text.Json.JsonCommentHandling.Skip
                    });
                    
                    System.Diagnostics.Debug.WriteLine($"Search API Response Code: {apiResponse?.Code}");
                    System.Diagnostics.Debug.WriteLine($"Search API Response Type: {apiResponse?.Type}");
                    System.Diagnostics.Debug.WriteLine($"Search API Response Result: {apiResponse?.Result != null}");
                    
                    if ((apiResponse?.Type == "success" || apiResponse?.Code == 200) && apiResponse.Result != null)
                    {
                        TotalCount = apiResponse.Result.Total;
                        TotalPages = apiResponse.Result.TotalPages;
                        
                        if (apiResponse.Result.Items != null && apiResponse.Result.Items.Length > 0)
                        {
                            int rowNumber = (CurrentPage - 1) * PageSize + 1;
                            foreach (var bom in apiResponse.Result.Items)
                            {
                                Boms.Add(new BomDisplayModel
                                {
                                    RowNumber = rowNumber++,
                                    Model = bom
                                });
                            }
                            
                            System.Diagnostics.Debug.WriteLine($"Search: Added {Boms.Count} BOM items to display");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"Search: No results found");
                            // 搜索无结果时更新页码信息
                            TotalCount = 0;
                            TotalPages = 0;
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"Search API response was not successful");
                        MessageBox.Show("搜索失败，请联系系统管理员", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Search API request failed: {response.StatusCode} - {content}");
                    MessageBox.Show($"搜索请求失败: {response.StatusCode}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Search Exception: {ex.Message}");
                MessageBox.Show($"搜索时发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                OnPropertyChanged(nameof(IsEmpty));
            }
        }

        private void Search()
        {
            CurrentPage = 1;
            if (string.IsNullOrWhiteSpace(SearchKeyword))
            {
                LoadData(); // 如果没有搜索关键词，使用默认加载
            }
            else
            {
                LoadDataWithSearch(); // 如果有搜索关键词，使用搜索加载
            }
        }
        
        private bool CanConfirm()
        {
            return SelectedBom != null;
        }
        
        private void Confirm()
        {
            if (SelectedBom != null)
            {
                CloseWindow?.Invoke(true, SelectedBom.Model);
            }
            else
            {
                MessageBox.Show("请选择一个BOM", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private void Cancel()
        {
            CloseWindow?.Invoke(false, null);
        }
        
        private void PrevPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
            }
        }
        
        private bool CanPrevPage()
        {
            return CurrentPage > 1;
        }
        
        private void NextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
            }
        }
        
        private bool CanNextPage()
        {
            return CurrentPage < TotalPages;
        }
        
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
    
    // 用于显示在DataGrid中的模型
    public class BomDisplayModel
    {
        public int RowNumber { get; set; }
        public BomModel Model { get; set; }
        
        public string BomCode => Model?.BomCode;
        public string BomVersion => Model?.BomVersion;
        public bool IsDefaultBom => Model?.IsDefaultBom ?? false;
        public int DailyOutput => Model?.DailyOutput ?? 0;
    }
    
    public class ApiResponse<T>
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public T Result { get; set; }
        public string Time { get; set; }
    }
    
    public class PagedResult<T>
    {
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int Total { get; set; }
        public int TotalPages { get; set; }
        public T[] Items { get; set; }
        public bool HasPrevPage { get; set; }
        public bool HasNextPage { get; set; }
    }
} 