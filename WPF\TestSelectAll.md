# 全选功能测试说明

## 修改内容

1. **修复了XAML中的绑定问题**：
   - 在 `PlanPage.xaml` 中，将全选复选框的绑定从 `{Binding IsAllSelected}` 修改为 `{Binding DataContext.IsAllSelected, RelativeSource={RelativeSource AncestorType=DataGrid}}`
   - 这样确保了全选复选框能正确绑定到ViewModel的IsAllSelected属性

2. **添加了选择状态改变事件**：
   - 在 `PlanModel.cs` 中添加了 `IsSelectedChanged` 事件
   - 当单个项目的选择状态改变时，会触发此事件通知ViewModel

3. **改进了ViewModel的全选逻辑**：
   - 在 `PlanViewModel.cs` 中添加了 `OnPlanIsSelectedChanged` 方法来处理单个项目选择状态改变
   - 修改了 `IsAllSelected` 属性的setter，避免无限循环
   - 改进了 `Plans` 属性的setter，自动订阅和取消订阅事件

## 测试步骤

1. 启动应用程序
2. 导航到生产计划页面
3. 测试以下场景：
   - 点击红框中的全选复选框，应该选中所有行
   - 再次点击全选复选框，应该取消选中所有行
   - 手动选中部分行，全选复选框应该保持未选中状态
   - 手动选中所有行，全选复选框应该自动变为选中状态
   - 在全选状态下取消选中任意一行，全选复选框应该自动变为未选中状态

## 预期结果

- 全选复选框能够正确控制所有行的选择状态
- 单个行的选择状态改变能够正确更新全选复选框的状态
- 不会出现无限循环或性能问题
