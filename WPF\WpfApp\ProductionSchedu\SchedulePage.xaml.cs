﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace WpfApp.ProductionSchedu
{
    /// <summary>
    /// SchedulePage.xaml 的交互逻辑
    /// </summary>
    public partial class SchedulePage : UserControl
    {
        public SchedulePage(long workOrderId)
        {
            InitializeComponent();
            //DataContext将在外部设置
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {

        }
    }
}
