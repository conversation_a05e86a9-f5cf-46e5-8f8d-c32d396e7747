using System;
using System.ComponentModel;
using System.Windows.Input;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using WpfApp.Bom;

namespace WpfApp
{
    public class BomEditViewModel : INotifyPropertyChanged
{
    private string _title;
    private string _bomCode;
    private string _bomVersion;
    private int _productId;
    private string _productCode;
    private string _productName;
    private string _specification;
    private string _unit;
    private int _dailyOutput;
    private bool _isSystemCode;
    private bool _isDefaultBom;
    private string _remarks;
    private string _processRouteId;
    private int _state;
    private int _processStepId;
    private string _materialIdsText;
    private string _materialIdswText;
    private ObservableCollection<MaterialItem> _materials;
    private List<int> _materialIDs;
    private string _selectedProcessRouteName;
    private string _selectedProcessRouteCode;
    private ObservableCollection<ProcessStepItem> _processSteps;
    private ProcessStepItem _selectedProcessStep;
    private bool _hasProcessSteps;
    private readonly HttpClient _httpClient;

    public event Action<bool> CloseRequested;
    public event Action SaveSucceeded;
    public event Action BackRequested;

    public string Title
    {
        get => _title;
        set { _title = value; OnPropertyChanged(nameof(Title)); }
    }

    public string BomCode
    {
        get => _bomCode;
        set { _bomCode = value; OnPropertyChanged(nameof(BomCode)); }
    }

    public string BomVersion
    {
        get => _bomVersion;
        set { _bomVersion = value; OnPropertyChanged(nameof(BomVersion)); }
    }

    public int ProductId
    {
        get => _productId;
        set { _productId = value; OnPropertyChanged(nameof(ProductId)); }
    }

    public string ProductCode
    {
        get => _productCode;
        set { _productCode = value; OnPropertyChanged(nameof(ProductCode)); }
    }

    public string ProductName
    {
        get => _productName;
        set { _productName = value; OnPropertyChanged(nameof(ProductName)); }
    }

    public string Specification
    {
        get => _specification;
        set { _specification = value; OnPropertyChanged(nameof(Specification)); }
    }

    public string Unit
    {
        get => _unit;
        set { _unit = value; OnPropertyChanged(nameof(Unit)); }
    }

    public int DailyOutput
    {
        get => _dailyOutput;
        set { _dailyOutput = value; OnPropertyChanged(nameof(DailyOutput)); }
    }

    public bool IsSystemCode
    {
        get => _isSystemCode;
        set { _isSystemCode = value; OnPropertyChanged(nameof(IsSystemCode)); }
    }

    public bool IsDefaultBom
    {
        get => _isDefaultBom;
        set { _isDefaultBom = value; OnPropertyChanged(nameof(IsDefaultBom)); }
    }

    public string Remarks
    {
        get => _remarks;
        set { _remarks = value; OnPropertyChanged(nameof(Remarks)); }
    }

    public string ProcessRouteId
    {
        get => _processRouteId;
        set { _processRouteId = value; OnPropertyChanged(nameof(ProcessRouteId)); }
    }

    public int State
    {
        get => _state;
        set { _state = value; OnPropertyChanged(nameof(State)); }
    }

    public int ProcessStepId
    {
        get => _processStepId;
        set { _processStepId = value; OnPropertyChanged(nameof(ProcessStepId)); }
    }

    public string MaterialIdsText
    {
        get => _materialIdsText;
        set { _materialIdsText = value; OnPropertyChanged(nameof(MaterialIdsText)); }
    }

    public string MaterialIdswText
    {
        get => _materialIdswText;
        set { _materialIdswText = value; OnPropertyChanged(nameof(MaterialIdswText)); }
    }

    public ObservableCollection<MaterialItem> Materials
    {
        get => _materials;
        set { _materials = value; OnPropertyChanged(nameof(Materials)); }
    }

    /// <summary>
    /// 物料ID列表
    /// </summary>
    public List<int> MaterialIDs
    {
        get => _materialIDs;
        set { _materialIDs = value; OnPropertyChanged(nameof(MaterialIDs)); }
    }

    /// <summary>
    /// 从Materials集合更新MaterialIDs
    /// </summary>
    public void UpdateMaterialIDs()
    {
        if (Materials != null)
        {
            var selectedMaterials = Materials.Where(m => m.IsSelected && m.Id > 0).ToList();
            MaterialIDs = selectedMaterials.Select(m => (int)m.Id).ToList();
            
            System.Diagnostics.Debug.WriteLine($"UpdateMaterialIDs: 选中物料数量: {selectedMaterials.Count}");
            foreach (var material in selectedMaterials)
            {
                System.Diagnostics.Debug.WriteLine($"  选中物料: ID={material.Id}, 编码={material.MaterialCode}, 名称={material.MaterialName}");
            }
            System.Diagnostics.Debug.WriteLine($"MaterialIDs数组: [{string.Join(", ", MaterialIDs)}]");
        }
        else
        {
            MaterialIDs = new List<int>();
            System.Diagnostics.Debug.WriteLine("UpdateMaterialIDs: Materials为null，清空MaterialIDs");
        }
    }

    /// <summary>
    /// 从工序物料集合更新MaterialIdsw
    /// </summary>
    public void UpdateMaterialIdsw()
    {
        var processStepMaterialIds = new List<int>();
        
        if (ProcessSteps != null)
        {
            foreach (var processStep in ProcessSteps)
            {
                if (processStep.Materials != null)
                {
                    var stepMaterialIds = processStep.Materials
                        .Where(m => m.IsSelected && m.Id > 0)
                        .Select(m => (int)m.Id)
                        .ToList();
                    
                    processStepMaterialIds.AddRange(stepMaterialIds);
                    
                    System.Diagnostics.Debug.WriteLine($"工序 {processStep.ProcessStepName} 选中物料数量: {stepMaterialIds.Count}");
                    foreach (var materialId in stepMaterialIds)
                    {
                        System.Diagnostics.Debug.WriteLine($"  工序物料ID: {materialId}");
                    }
                }
            }
        }
        
        // 将MaterialIdswText设置为逗号分隔的字符串
        MaterialIdswText = string.Join(",", processStepMaterialIds);
        
        System.Diagnostics.Debug.WriteLine($"UpdateMaterialIdsw: 工序物料总数量: {processStepMaterialIds.Count}");
        System.Diagnostics.Debug.WriteLine($"MaterialIdswText: {MaterialIdswText}");
    }

    /// <summary>
    /// 选中的工艺路线名称
    /// </summary>
    public string SelectedProcessRouteName
    {
        get => _selectedProcessRouteName;
        set
        {
            _selectedProcessRouteName = value;
            OnPropertyChanged(nameof(SelectedProcessRouteName));
            System.Diagnostics.Debug.WriteLine($"SelectedProcessRouteName 属性已更新: {value}");
        }
    }

    /// <summary>
    /// 选中的工艺路线编号
    /// </summary>
    public string SelectedProcessRouteCode
    {
        get => _selectedProcessRouteCode;
        set
        {
            _selectedProcessRouteCode = value;
            OnPropertyChanged(nameof(SelectedProcessRouteCode));
            System.Diagnostics.Debug.WriteLine($"SelectedProcessRouteCode 属性已更新: {value}");
        }
    }

    /// <summary>
    /// 工序列表
    /// </summary>
    public ObservableCollection<ProcessStepItem> ProcessSteps
    {
        get => _processSteps ?? (_processSteps = new ObservableCollection<ProcessStepItem>());
        set
        {
            _processSteps = value;
            OnPropertyChanged(nameof(ProcessSteps));
        }
    }

    /// <summary>
    /// 当前选中的工序
    /// </summary>
    public ProcessStepItem SelectedProcessStep
    {
        get => _selectedProcessStep;
        set
        {
            // 取消之前选中的工序
            if (_selectedProcessStep != null)
            {
                _selectedProcessStep.IsSelected = false;
            }

            _selectedProcessStep = value;

            // 设置新选中的工序
            if (_selectedProcessStep != null)
            {
                System.Diagnostics.Debug.WriteLine($"=== 开始设置工序 ===");
                System.Diagnostics.Debug.WriteLine($"工序名称: {_selectedProcessStep.ProcessStepName}");
                System.Diagnostics.Debug.WriteLine($"工序ID: {_selectedProcessStep.Id}");
                System.Diagnostics.Debug.WriteLine($"工序ID类型: {_selectedProcessStep.Id?.GetType().Name}");
                
                _selectedProcessStep.IsSelected = true;
                // 切换到该工序的物料列表
                Materials = _selectedProcessStep.Materials;
                
                // 设置ProcessStepId
                if (!string.IsNullOrEmpty(_selectedProcessStep.Id))
                {
                    if (int.TryParse(_selectedProcessStep.Id, out int processStepId))
                    {
                        ProcessStepId = processStepId;
                        System.Diagnostics.Debug.WriteLine($"成功设置ProcessStepId: {ProcessStepId}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"无法解析ProcessStepId: {_selectedProcessStep.Id}");
                        System.Diagnostics.Debug.WriteLine($"尝试解析的原始值: '{_selectedProcessStep.Id}'");
                        
                        // 尝试使用StepOrder作为备用
                        ProcessStepId = _selectedProcessStep.StepOrder;
                        System.Diagnostics.Debug.WriteLine($"使用StepOrder作为备用ProcessStepId: {ProcessStepId}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("工序ID为空，使用StepOrder作为备用");
                    ProcessStepId = _selectedProcessStep.StepOrder;
                    System.Diagnostics.Debug.WriteLine($"使用StepOrder作为ProcessStepId: {ProcessStepId}");
                }
                
                System.Diagnostics.Debug.WriteLine($"=== 工序设置完成 ===");
            }

            OnPropertyChanged(nameof(SelectedProcessStep));
            OnPropertyChanged(nameof(Materials));
            System.Diagnostics.Debug.WriteLine($"切换到工序: {_selectedProcessStep?.ProcessStepName}");
        }
    }

    /// <summary>
    /// 是否有工序数据
    /// </summary>
    public bool HasProcessSteps
    {
        get => _hasProcessSteps;
        set
        {
            _hasProcessSteps = value;
            OnPropertyChanged(nameof(HasProcessSteps));
        }
    }

    /// <summary>
    /// 是否全选
    /// </summary>
    public bool? IsAllSelected
    {
        get
        {
            if (Materials == null || Materials.Count == 0) return false;

            var selectedCount = Materials.Count(m => m.IsSelected);
            if (selectedCount == 0) return false;
            if (selectedCount == Materials.Count) return true;
            return null; // 部分选中状态
        }
        set
        {
            System.Diagnostics.Debug.WriteLine($"IsAllSelected setter called with value: {value}");
            if (Materials != null)
            {
                // 简化逻辑：如果当前是false或null，设为true；如果是true，设为false
                bool newState = value == true ? false : true;

                System.Diagnostics.Debug.WriteLine($"Setting all materials to: {newState}");
                foreach (var material in Materials)
                {
                    material.IsSelected = newState;
                }
                OnPropertyChanged(nameof(IsAllSelected));
            }
        }
    }

    public ICommand SaveCommand { get; }
    public ICommand CancelCommand { get; }
    public ICommand BackCommand { get; }
    public ICommand SelectProductCommand { get; }
    public ICommand AddMaterialCommand { get; }
    public ICommand RemoveMaterialCommand { get; }
    public ICommand DeleteMaterialCommand { get; }
    public ICommand SelectProcessRouteCommand { get; }
    public ICommand RemoveProcessRouteCommand { get; }
    public ICommand DebugFieldsCommand { get; }

    public BomEditViewModel(BomProduct bomProduct = null)
    {
        _httpClient = new HttpClient();

        SaveCommand = new WpfApp.Bom.RelayCommand(_ => Save());
        CancelCommand = new WpfApp.Bom.RelayCommand(_ => Cancel());
        BackCommand = new WpfApp.Bom.RelayCommand(_ => Back());
        SelectProductCommand = new WpfApp.Bom.RelayCommand(_ => SelectProduct());
        AddMaterialCommand = new WpfApp.Bom.RelayCommand(_ => AddMaterial());
        RemoveMaterialCommand = new WpfApp.Bom.RelayCommand(_ => RemoveMaterial());
        DeleteMaterialCommand = new WpfApp.Bom.RelayCommand(DeleteMaterial);
        SelectProcessRouteCommand = new WpfApp.Bom.RelayCommand(_ => SelectProcessRoute());
        RemoveProcessRouteCommand = new WpfApp.Bom.RelayCommand(_ => RemoveProcessRoute());

        Materials = new ObservableCollection<MaterialItem>();
        ProcessSteps = new ObservableCollection<ProcessStepItem>();

        if (bomProduct != null)
        {
            Title = "编辑BOM";
            LoadFromProduct(bomProduct);
        }
        else
        {
            Title = "新增BOM";
            SetDefaults();
        }

        DebugFieldsCommand = new WpfApp.Bom.RelayCommand(_ => DebugPrintAllFields());

    }




    public BomEditViewModel(BomEditModel editModel)
    {
        _httpClient = new HttpClient();

        SaveCommand = new WpfApp.Bom.RelayCommand(_ => Save());
        CancelCommand = new WpfApp.Bom.RelayCommand(_ => Cancel());
        BackCommand = new WpfApp.Bom.RelayCommand(_ => Back());
        SelectProductCommand = new WpfApp.Bom.RelayCommand(_ => SelectProduct());
        AddMaterialCommand = new WpfApp.Bom.RelayCommand(_ => AddMaterial());
        RemoveMaterialCommand = new WpfApp.Bom.RelayCommand(_ => RemoveMaterial());
        DeleteMaterialCommand = new WpfApp.Bom.RelayCommand(DeleteMaterial);
        SelectProcessRouteCommand = new WpfApp.Bom.RelayCommand(_ => SelectProcessRoute());
        RemoveProcessRouteCommand = new WpfApp.Bom.RelayCommand(_ => RemoveProcessRoute());

        Materials = new ObservableCollection<MaterialItem>();
        ProcessSteps = new ObservableCollection<ProcessStepItem>();
        Title = "编辑BOM";
        LoadFromEditModel(editModel);
        
                DebugFieldsCommand = new WpfApp.Bom.RelayCommand(_ => DebugPrintAllFields());
    }

    private void LoadFromProduct(BomProduct product)
    {
        BomCode = product.BomCode;
        BomVersion = product.BomVersion;
        ProductId = product.ProductId;
        ProductCode = product.ProductCode;
        Specification = product.Specification;
        Unit = product.Unit;
        DailyOutput = product.DailyOutput;
        IsSystemCode = product.IsSystemCode;
        IsDefaultBom = product.IsDefaultBom;
        Remarks = product.Remarks;
        ProcessRouteId = product.ProcessRouteId;

        // 显示模型只有基本字段，其他字段设为默认值
        State = 0;
        ProcessStepId = 0;
        MaterialIdsText = "";
        MaterialIdswText = "";

        // 如果有工艺路线编号，加载对应的工序
        if (!string.IsNullOrEmpty(SelectedProcessRouteCode))
        {
            _ = LoadProcessStepsAsync(SelectedProcessRouteCode);
        }
    }

    private void LoadFromEditModel(BomEditModel editModel)
    {
        BomCode = editModel.BomCode;
        BomVersion = editModel.BomVersion;
        ProductId = editModel.ProductId;
        ProductCode = editModel.ProductCode;
        Specification = editModel.Specification;
        Unit = editModel.Unit;
        DailyOutput = editModel.DailyOutput;
        IsSystemCode = editModel.IsSystemCode;
        IsDefaultBom = editModel.IsDefaultBom;
        Remarks = editModel.Remarks;
        ProcessRouteId = editModel.ProcessRouteId;
        State = editModel.State;
        ProcessStepId = editModel.ProcessStepId;

        // 将物料ID列表转换为逗号分隔的字符串
        if (editModel.MaterialIDs != null && editModel.MaterialIDs.Any())
        {
            MaterialIdsText = string.Join(",", editModel.MaterialIDs);
        }
        else
        {
            MaterialIdsText = "";
        }

        if (editModel.MaterialIdsw != null && editModel.MaterialIdsw.Any())
        {
            MaterialIdswText = string.Join(",", editModel.MaterialIdsw);
        }
        else
        {
            MaterialIdswText = "";
        }

        // 如果有工艺路线编号，加载对应的工序
        if (!string.IsNullOrEmpty(SelectedProcessRouteCode))
        {
            _ = LoadProcessStepsAsync(SelectedProcessRouteCode);
        }
        else if (!string.IsNullOrEmpty(ProcessRouteId))
        {
            // 如果只有工艺路线ID但没有编号，提示用户重新选择工艺路线
            Console.WriteLine($"检测到工艺路线ID: {ProcessRouteId}，但缺少工艺路线编号，请重新选择工艺路线");
        }
    }

    private void SetDefaults()
    {
        BomCode = "";
        BomVersion = "1.0";
        ProductId = 0;
        ProductCode = "";
        Specification = "";
        Unit = "";
        DailyOutput = 0;
        IsSystemCode = false;
        IsDefaultBom = false;
        Remarks = "";
        ProcessRouteId = "";
        State = 0;
        ProcessStepId = 0;
        MaterialIdsText = "";
        MaterialIdswText = "";
        SelectedProcessRouteName = "";
        SelectedProcessRouteCode = "";
    }

    public BomEditModel ToEditModel()
    {
        var editModel = new BomEditModel
        {
            BomCode = BomCode,
            BomVersion = BomVersion,
            ProductId = ProductId,
            ProductCode = ProductCode,
            Specification = Specification,
            Unit = Unit,
            DailyOutput = DailyOutput,
            IsSystemCode = IsSystemCode,
            IsDefaultBom = IsDefaultBom,
            Remarks = Remarks,
            ProcessRouteId = ProcessRouteId,
            State = State,
            ProcessStepId = ProcessStepId
        };

        // 设置MaterialIDs - 优先使用MaterialIDs属性，如果为空则解析MaterialIdsText字符串
        if (MaterialIDs != null && MaterialIDs.Count > 0)
        {
            editModel.MaterialIDs = new List<int>(MaterialIDs);
        }
        else if (!string.IsNullOrWhiteSpace(MaterialIdsText))
        {
            try
            {
                editModel.MaterialIDs = MaterialIdsText.Split(',')
                    .Where(s => !string.IsNullOrWhiteSpace(s))
                    .Select(s => int.Parse(s.Trim()))
                    .ToList();
            }
            catch
            {
                editModel.MaterialIDs = new List<int>();
            }
        }
        else
        {
            editModel.MaterialIDs = new List<int>();
        }

        // 解析MaterialIdsw字符串
        if (!string.IsNullOrWhiteSpace(MaterialIdswText))
        {
            try
            {
                editModel.MaterialIdsw = MaterialIdswText.Split(',')
                    .Where(s => !string.IsNullOrWhiteSpace(s))
                    .Select(s => int.Parse(s.Trim()))
                    .ToList();
            }
            catch
            {
                editModel.MaterialIdsw = new List<int>();
            }
        }

        return editModel;
    }

    public BomProduct ToProduct()
    {
        return ToEditModel().ToBomProduct();
    }

    private async void Save()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("=== 开始保存BOM ===");
            
            // 打印当前所有字段值用于调试
            DebugPrintAllFields();
            
            // 强制设置所有字段值
            ForceSetAllFields();
            
            // 验证必填字段
            if (string.IsNullOrWhiteSpace(BomCode))
            {
                System.Windows.MessageBox.Show("BOM编码不能为空", "验证错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(ProductCode))
            {
                System.Windows.MessageBox.Show("产品编码不能为空", "验证错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }

            // 新增：工艺路线和工序必选校验
            if (string.IsNullOrEmpty(ProcessRouteId) || ProcessRouteId == "0" || ProcessStepId == 0)
            {
                System.Windows.MessageBox.Show("请先选择工艺路线和工序！", "验证错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }

            // 保存前更新MaterialIDs
            System.Diagnostics.Debug.WriteLine("调用UpdateMaterialIDs更新物料ID列表");
            UpdateMaterialIDs();
            
            System.Diagnostics.Debug.WriteLine($"保存前MaterialIDs数量: {MaterialIDs?.Count ?? 0}");
            if (MaterialIDs != null && MaterialIDs.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"MaterialIDs内容: [{string.Join(", ", MaterialIDs)}]");
            }

            // 调用API保存BOM
            var success = await SaveBomToApi();

            if (success)
            {
                System.Windows.MessageBox.Show("BOM保存成功", "成功", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                // 对于对话框模式
                CloseRequested?.Invoke(true);

                // 对于页面导航模式
                SaveSucceeded?.Invoke();
            }
            else
            {
                System.Windows.MessageBox.Show("BOM保存失败", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"保存时发生错误: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            System.Diagnostics.Debug.WriteLine($"保存BOM时出错: {ex}");
        }
    }

    private void Cancel()
    {
        CloseRequested?.Invoke(false);
    }

    private void Back()
    {
        BackRequested?.Invoke();
    }

    private void SelectProduct()
    {
        var productSelectViewModel = new ProductSelectViewModel();
        var dialog = new ProductSelectDialog(productSelectViewModel);

        if (Application.Current.MainWindow != null)
        {
            dialog.Owner = Application.Current.MainWindow;
        }

        if (dialog.ShowDialog() == true)
        {
            // 如果用户选择了产品，反填产品信息到BOM编辑页面
            if (productSelectViewModel.SelectedProduct != null)
            {
                var selectedProduct = productSelectViewModel.SelectedProduct;

                // 反填产品信息
                ProductId = (int)selectedProduct.Id;
                ProductCode = selectedProduct.ProductCode ?? "";
                ProductName = selectedProduct.ProductName ?? "";
                Specification = selectedProduct.Specification ?? "";
                Unit = selectedProduct.Unit ?? "";
                IsSystemCode = selectedProduct.IsSystemCode;

                System.Diagnostics.Debug.WriteLine($"产品信息已反填: ID={ProductId}, 编码={ProductCode}, 名称={ProductName}");
            }
        }
    }

        private void AddMaterial()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("AddMaterial方法被调用");

                var dialog = new MaterialSelectDialog();
                dialog.Owner = System.Windows.Application.Current.MainWindow;

                System.Diagnostics.Debug.WriteLine("物料选择对话框已创建");

                if (dialog.ShowDialog() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"对话框返回true，选中物料数量: {dialog.SelectedMaterials?.Count ?? 0}");

                    // 将选中的物料添加到当前BOM的物料列表中
                    foreach (var selectedMaterial in dialog.SelectedMaterials)
                    {
                        System.Diagnostics.Debug.WriteLine($"处理物料: {selectedMaterial.MaterialCode} - {selectedMaterial.MaterialName}");

                        // 检查是否已经存在相同的物料
                        if (!Materials.Any(m => m.MaterialCode == selectedMaterial.MaterialCode))
                        {
                            var materialItem = new MaterialItem();
                            System.Diagnostics.Debug.WriteLine("MaterialItem对象已创建");

                            materialItem.Index = Materials.Count + 1;
                            System.Diagnostics.Debug.WriteLine($"设置Index: {materialItem.Index}");

                            // 设置物料ID - 这是关键修复
                            materialItem.Id = selectedMaterial.Id;
                            System.Diagnostics.Debug.WriteLine($"设置Id: {materialItem.Id}");

                            materialItem.MaterialCode = selectedMaterial.MaterialCode;
                            System.Diagnostics.Debug.WriteLine($"设置MaterialCode完成");

                            materialItem.MaterialName = selectedMaterial.MaterialName;
                            System.Diagnostics.Debug.WriteLine($"设置MaterialName完成");

                            materialItem.Specification = selectedMaterial.Specification;
                            System.Diagnostics.Debug.WriteLine($"设置Specification完成");

                            materialItem.Unit = selectedMaterial.Unit;
                            System.Diagnostics.Debug.WriteLine($"设置Unit完成");

                            System.Diagnostics.Debug.WriteLine($"最终MaterialItem属性值:");
                            System.Diagnostics.Debug.WriteLine($"  Id: {materialItem.Id}");
                            System.Diagnostics.Debug.WriteLine($"  MaterialCode: '{materialItem.MaterialCode}'");
                            System.Diagnostics.Debug.WriteLine($"  MaterialName: '{materialItem.MaterialName}'");
                            System.Diagnostics.Debug.WriteLine($"  Specification: '{materialItem.Specification}'");
                            System.Diagnostics.Debug.WriteLine($"  Unit: '{materialItem.Unit}'");

                            materialItem.PropertyChanged += MaterialItem_PropertyChanged;
                            Materials.Add(materialItem);
                            System.Diagnostics.Debug.WriteLine($"物料已添加到Materials集合，当前总数: {Materials.Count}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"物料 {selectedMaterial.MaterialCode} 已存在，跳过添加");
                        }
                    }

                    // 更新序号
                    UpdateRowNumbers();
                    System.Diagnostics.Debug.WriteLine("序号更新完成");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("对话框被取消或返回false");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AddMaterial方法出现异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            }
        }

        private void UpdateRowNumbers()
        {
            for (int i = 0; i < Materials.Count; i++)
            {
                Materials[i].Index = i + 1;
            }
        }

        private void RemoveMaterial()
        {
            var selectedMaterials = Materials.Where(m => m.IsSelected).ToList();
            if (selectedMaterials.Any())
            {
                foreach (var material in selectedMaterials)
                {
                    material.PropertyChanged -= MaterialItem_PropertyChanged;
                    Materials.Remove(material);
                }

                // 重新编号
                UpdateRowNumbers();

                System.Diagnostics.Debug.WriteLine($"移除了 {selectedMaterials.Count} 个物料");
            }
            else
            {
                System.Windows.MessageBox.Show("请先选择要移除的物料", "提示",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
        }

        private void DeleteMaterial(object parameter)
        {
            if (parameter is MaterialItem material)
            {
                material.PropertyChanged -= MaterialItem_PropertyChanged;
                Materials.Remove(material);

                // 重新编号
                for (int i = 0; i < Materials.Count; i++)
                {
                    Materials[i].Index = i + 1;
                }

                System.Diagnostics.Debug.WriteLine($"删除物料: {material.MaterialName}");

                // 更新全选状态
                OnPropertyChanged(nameof(IsAllSelected));
            }
        }



        /// <summary>
        /// 物料选择状态变化处理
        /// </summary>
        private void MaterialItem_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(MaterialItem.IsSelected))
            {
                // 通知页面更新全选CheckBox状态
                if (System.Windows.Application.Current.MainWindow is MainWindow mainWindow)
                {
                    // 这里需要找到当前的BomEditPage并调用UpdateSelectAllCheckBox
                    // 由于架构限制，我们暂时使用事件通知的方式
                }
                OnPropertyChanged(nameof(IsAllSelected));
                
                // 新增：实时更新工序物料ID列表
                UpdateMaterialIdsw();
                System.Diagnostics.Debug.WriteLine("物料选择状态变化，已更新MaterialIdsw");
            }
        }

        /// <summary>
        /// 选择工艺路线
        /// </summary>
        private void SelectProcessRoute()
        {
            try
            {
                var dialog = new ProcessRouteSelectDialog();

                // 设置父窗口
                if (System.Windows.Application.Current.MainWindow != null)
                {
                    dialog.Owner = System.Windows.Application.Current.MainWindow;
                }

                var result = dialog.ShowDialog();

                if (result == true && dialog.SelectedProcessRoute != null)
                {
                    var selectedRoute = dialog.SelectedProcessRoute;

                    System.Diagnostics.Debug.WriteLine($"=== 工艺路线选择成功 ===");
                    System.Diagnostics.Debug.WriteLine($"选择的工艺路线ID: {selectedRoute.Id}");
                    System.Diagnostics.Debug.WriteLine($"选择的工艺路线ID类型: {selectedRoute.Id?.GetType().Name}");
                    System.Diagnostics.Debug.WriteLine($"选择的工艺路线名称: {selectedRoute.ProcessRouteName}");
                    System.Diagnostics.Debug.WriteLine($"选择的工艺路线编号: {selectedRoute.ProcessRouteCode}");

                    SelectedProcessRouteName = selectedRoute.ProcessRouteName;
                    SelectedProcessRouteCode = selectedRoute.ProcessRouteCode;
                    
                    // 优先使用Id，如果Id为空则使用ProcessRouteCode
                    if (!string.IsNullOrEmpty(selectedRoute.Id))
                    {
                        ProcessRouteId = selectedRoute.Id;
                        System.Diagnostics.Debug.WriteLine($"使用工艺路线ID: {ProcessRouteId}");
                    }
                    else if (!string.IsNullOrEmpty(selectedRoute.ProcessRouteCode))
                    {
                        ProcessRouteId = selectedRoute.ProcessRouteCode;
                        System.Diagnostics.Debug.WriteLine($"使用工艺路线编码作为ID: {ProcessRouteId}");
                    }
                    else
                    {
                        ProcessRouteId = string.Empty;
                        System.Diagnostics.Debug.WriteLine("工艺路线ID和编码都为空");
                    }

                    System.Diagnostics.Debug.WriteLine($"=== 属性设置完成 ===");
                    System.Diagnostics.Debug.WriteLine($"ViewModel.SelectedProcessRouteName: {SelectedProcessRouteName}");
                    System.Diagnostics.Debug.WriteLine($"ViewModel.SelectedProcessRouteCode: {SelectedProcessRouteCode}");
                    System.Diagnostics.Debug.WriteLine($"ViewModel.ProcessRouteId: {ProcessRouteId}");
                    System.Diagnostics.Debug.WriteLine($"原始selectedRoute.Id: {selectedRoute.Id}");
                    System.Diagnostics.Debug.WriteLine($"设置后的ProcessRouteId: {ProcessRouteId}");
                    System.Diagnostics.Debug.WriteLine($"ProcessRouteId类型: {ProcessRouteId?.GetType().Name}");

                    // 选择工艺路线后，加载对应的工序
                    Console.WriteLine($"=== 准备加载工序 ===");
                    Console.WriteLine($"即将调用LoadProcessStepsAsync，ProcessRouteCode: {SelectedProcessRouteCode}");
                    _ = LoadProcessStepsAsync(SelectedProcessRouteCode);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"=== 工艺路线选择取消或失败 ===");
                    System.Diagnostics.Debug.WriteLine($"Dialog Result: {result}");
                    System.Diagnostics.Debug.WriteLine($"Selected ProcessRoute: {dialog.SelectedProcessRoute}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"选择工艺路线时出错: {ex.Message}");

                // 如果弹窗出错，清空数据
                SelectedProcessRouteName = "";
                SelectedProcessRouteCode = "";
                ProcessRouteId = "";
            }
        }

        /// <summary>
        /// 移除工艺路线
        /// </summary>
        private void RemoveProcessRoute()
        {
            SelectedProcessRouteName = "";
            SelectedProcessRouteCode = "";
            ProcessRouteId = "";

            // 清空工序列表
            ProcessSteps.Clear();
            HasProcessSteps = false;
            SelectedProcessStep = null;

            System.Diagnostics.Debug.WriteLine("已移除工艺路线");
        }

        /// <summary>
        /// 根据工艺路线编号加载工序列表
        /// </summary>
        private async Task LoadProcessStepsAsync(string processRouteCode)
        {
            if (string.IsNullOrEmpty(processRouteCode))
            {
                System.Diagnostics.Debug.WriteLine("工艺路线编号为空，无法加载工序");
                return;
            }

            try
            {
                Console.WriteLine($"=== 开始加载工序列表 ===");
                Console.WriteLine($"工艺路线编号: {processRouteCode}");
                System.Diagnostics.Debug.WriteLine($"=== 开始加载工序列表 ===");
                System.Diagnostics.Debug.WriteLine($"工艺路线编号: {processRouteCode}");

                // 构建API请求URL - 使用工艺路线编号作为路径参数
                var url = $"http://localhost:5005/api/bom/pageProcessComposition/1/11/{processRouteCode}";
                Console.WriteLine($"请求工序API: {url}");
                System.Diagnostics.Debug.WriteLine($"请求工序API: {url}");

                // 发送HTTP请求
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"API响应成功，状态码: {response.StatusCode}");
                    Console.WriteLine($"API响应内容: {jsonContent}");
                    System.Diagnostics.Debug.WriteLine($"API响应: {jsonContent}");

                    try
                    {
                        var apiResponse = JsonSerializer.Deserialize<ProcessStepApiResponse>(jsonContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        System.Diagnostics.Debug.WriteLine($"API响应解析成功");
                        System.Diagnostics.Debug.WriteLine($"Code: {apiResponse?.Code}");
                        System.Diagnostics.Debug.WriteLine($"Message: {apiResponse?.Message}");
                        System.Diagnostics.Debug.WriteLine($"Result不为空: {apiResponse?.Result != null}");

                        if (apiResponse?.Result?.Items != null)
                        {
                            var result = apiResponse.Result;
                            System.Diagnostics.Debug.WriteLine($"工序数量: {result.Items.Length}");

                            ProcessSteps.Clear();

                            // 按工序顺序排序（使用SerialCode）
                            var sortedSteps = result.Items.OrderBy(s =>
                            {
                                // 尝试将SerialCode转换为数字进行排序
                                if (int.TryParse(s.SerialCode, out int order))
                                    return order;
                                return 999; // 如果无法解析，放到最后
                            }).ToArray();

                            foreach (var stepData in sortedSteps)
                            {
                                System.Diagnostics.Debug.WriteLine($"=== 处理工序数据 ===");
                                System.Diagnostics.Debug.WriteLine($"Id: {stepData.ProcessStepId}");
                                System.Diagnostics.Debug.WriteLine($"SerialCode: {stepData.SerialCode}");
                                System.Diagnostics.Debug.WriteLine($"ProcessName: {stepData.ProcessName}");
                                System.Diagnostics.Debug.WriteLine($"NextProcedure: {stepData.NextProcedure}");
                                System.Diagnostics.Debug.WriteLine($"IsSystemCode: {stepData.IsSystemCode}");
                                System.Diagnostics.Debug.WriteLine($"HasNextStep: {stepData.HasNextStep}");

                                var stepItem = new ProcessStepItem
                                {
                                    Id = stepData.ProcessStepId.ToString(), // 确保转换为字符串
                                    ProcessStepName = stepData.ProcessName ?? string.Empty,
                                    ProcessStepCode = stepData.SerialCode ?? string.Empty,
                                    Description = stepData.NextProcedure.ToString(),
                                    StepOrder = stepData.StepOrder,
                                    ProcessRouteId = stepData.ProcessRouteId.ToString(),
                                    IsSystemCode = stepData.IsSystemCode,
                                    CreateTime = stepData.CreateTime ?? string.Empty,
                                    UpdateTime = stepData.UpdateTime ?? string.Empty,
                                    CreatedUserId = stepData.CreatedUserId?.ToString() ?? string.Empty,
                                    CreatedUserName = stepData.CreatedUserName ?? string.Empty,
                                    UpdatedUserId = stepData.UpdatedUserId?.ToString() ?? string.Empty,
                                    UpdatedUserName = stepData.UpdatedUserName ?? string.Empty,
                                    Remarks = stepData.Remarks ?? string.Empty
                                };
                                
                                System.Diagnostics.Debug.WriteLine($"创建工序项: ID={stepItem.Id}, 名称={stepItem.ProcessStepName}, 原始ID={stepData.Id}");

                                ProcessSteps.Add(stepItem);
                                System.Diagnostics.Debug.WriteLine($"添加工序: {stepItem.StepOrder} - {stepItem.ProcessStepName}, ID: {stepItem.Id}");
                            }

                            HasProcessSteps = ProcessSteps.Count > 0;

                            // 默认选择第一个工序
                            if (ProcessSteps.Count > 0)
                            {
                                SelectedProcessStep = ProcessSteps[0];
                                System.Diagnostics.Debug.WriteLine($"默认选择第一个工序: {SelectedProcessStep.ProcessStepName}, ID: {SelectedProcessStep.Id}");
                            }

                            System.Diagnostics.Debug.WriteLine($"工序加载完成，共 {ProcessSteps.Count} 个工序");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("API响应中没有工序数据");
                            ProcessSteps.Clear();
                            HasProcessSteps = false;
                        }
                    }
                    catch (JsonException jsonEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"JSON反序列化失败: {jsonEx.Message}");
                        System.Diagnostics.Debug.WriteLine($"原始JSON: {jsonContent}");
                        ProcessSteps.Clear();
                        HasProcessSteps = false;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"工序API请求失败: {response.StatusCode} - {response.ReasonPhrase}");
                    Console.WriteLine($"错误内容: {errorContent}");
                    System.Diagnostics.Debug.WriteLine($"工序API请求失败: {response.StatusCode} - {response.ReasonPhrase}");
                    System.Diagnostics.Debug.WriteLine($"错误内容: {errorContent}");
                    ProcessSteps.Clear();
                    HasProcessSteps = false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载工序时出错: {ex.Message}");
                Console.WriteLine($"异常详情: {ex}");
                System.Diagnostics.Debug.WriteLine($"加载工序时出错: {ex.Message}");
                ProcessSteps.Clear();
                HasProcessSteps = false;
            }
        }

    public event PropertyChangedEventHandler PropertyChanged;
    protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));

    /// <summary>
    /// 保存BOM到API
    /// </summary>
    private async Task<bool> SaveBomToApi()
    {
        try
        {
            // 确保在保存前更新所有物料ID列表
            UpdateMaterialIDs();
            UpdateMaterialIdsw();
            
            // 确保ProcessStepId有值
            if (ProcessStepId == 0 && SelectedProcessStep != null)
            {
                if (int.TryParse(SelectedProcessStep.Id, out int processStepId))
                {
                    ProcessStepId = processStepId;
                    System.Diagnostics.Debug.WriteLine($"保存时强制设置ProcessStepId: {ProcessStepId}");
                }
            }
            
            // 确保ProcessRouteId有值
            if (string.IsNullOrEmpty(ProcessRouteId) && !string.IsNullOrEmpty(SelectedProcessRouteCode))
            {
                // 尝试从SelectedProcessRouteCode获取ID
                ProcessRouteId = SelectedProcessRouteCode;
                System.Diagnostics.Debug.WriteLine($"保存时强制设置ProcessRouteId: {ProcessRouteId}");
            }
            
            // 解析MaterialIdsw字符串为数组
            var materialIdswArray = new int[0];
            if (!string.IsNullOrWhiteSpace(MaterialIdswText))
            {
                try
                {
                    materialIdswArray = MaterialIdswText.Split(',')
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .Select(s => int.Parse(s.Trim()))
                        .ToArray();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"解析MaterialIdsw失败: {ex.Message}");
                }
            }
            
            // 解析ProcessRouteId字符串为整数
            long processRouteId = 0;
            if (!string.IsNullOrEmpty(ProcessRouteId))
            {
                if (long.TryParse(ProcessRouteId, out processRouteId))
                {
                    System.Diagnostics.Debug.WriteLine($"成功解析ProcessRouteId: {ProcessRouteId} -> {processRouteId}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"无法解析ProcessRouteId: {ProcessRouteId}");
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("ProcessRouteId为空");
            }

            // 构建BOM数据传输对象
            var bomDto = new BomCreateDto
            {
                BomCode = BomCode,
                BomVersion = BomVersion,
                ProductId = ProductId, // 设置ProductId
                ProductCode = ProductCode,
                Specification = Specification,
                Unit = Unit,
                DailyOutput = DailyOutput,
                Remarks = Remarks,
                State = State > 0 ? State : 1, // 设置默认状态为1
                ProcessRouteId = processRouteId, // 设置ProcessRouteId
                ProcessStepId = ProcessStepId, // 设置ProcessStepId
                MaterialIds = MaterialIDs?.ToArray() ?? new int[0],
                MaterialIdsw = materialIdswArray // 设置工序物料ID数组
            };

            Console.WriteLine($"=== 准备保存BOM ===");
            Console.WriteLine($"BOM编码: {bomDto.BomCode}");
            Console.WriteLine($"产品编码: {bomDto.ProductCode}");
            Console.WriteLine($"产品ID: {bomDto.ProductId}");
            Console.WriteLine($"工艺路线ID: {bomDto.ProcessRouteId}");
            Console.WriteLine($"工序ID: {bomDto.ProcessStepId}");
            Console.WriteLine($"状态: {bomDto.State}");
            System.Diagnostics.Debug.WriteLine($"保存时的ProcessRouteId: {ProcessRouteId}");
            System.Diagnostics.Debug.WriteLine($"保存时的ProcessStepId: {ProcessStepId}");
            Console.WriteLine($"物料数量: {bomDto.MaterialIds.Length}");
            Console.WriteLine($"工序物料数量: {bomDto.MaterialIdsw.Length}");
            
            if (bomDto.MaterialIds.Length > 0)
            {
                Console.WriteLine($"物料IDs: [{string.Join(", ", bomDto.MaterialIds)}]");
            }
            if (bomDto.MaterialIdsw.Length > 0)
            {
                Console.WriteLine($"工序物料IDs: [{string.Join(", ", bomDto.MaterialIdsw)}]");
            }

            // 序列化为JSON
            var json = JsonSerializer.Serialize(bomDto, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });

            Console.WriteLine($"请求JSON: {json}");

            // 发送POST请求
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync("http://localhost:5005/api/bom/bom", content);

            Console.WriteLine($"API响应状态码: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"API响应内容: {responseContent}");
                return true;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"API错误响应: {errorContent}");
                return false;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"保存BOM时发生异常: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"保存BOM异常详情: {ex}");
            return false;
        }
    }

    /// <summary>
    /// 调试方法：打印当前所有字段值
    /// </summary>
    public void DebugPrintAllFields()
    {
        System.Diagnostics.Debug.WriteLine("=== 当前所有字段值 ===");
        System.Diagnostics.Debug.WriteLine($"ProductId: {ProductId}");
        System.Diagnostics.Debug.WriteLine($"ProcessRouteId: {ProcessRouteId}");
        System.Diagnostics.Debug.WriteLine($"ProcessStepId: {ProcessStepId}");
        System.Diagnostics.Debug.WriteLine($"SelectedProcessRouteName: {SelectedProcessRouteName}");
        System.Diagnostics.Debug.WriteLine($"SelectedProcessRouteCode: {SelectedProcessRouteCode}");
        System.Diagnostics.Debug.WriteLine($"SelectedProcessStep: {SelectedProcessStep?.ProcessStepName}");
        System.Diagnostics.Debug.WriteLine($"SelectedProcessStep.Id: {SelectedProcessStep?.Id}");
        System.Diagnostics.Debug.WriteLine($"SelectedProcessStep.StepOrder: {SelectedProcessStep?.StepOrder}");
        System.Diagnostics.Debug.WriteLine($"MaterialIDs.Count: {MaterialIDs?.Count ?? 0}");
        System.Diagnostics.Debug.WriteLine($"MaterialIdswText: {MaterialIdswText}");
        System.Diagnostics.Debug.WriteLine($"ProcessSteps.Count: {ProcessSteps?.Count ?? 0}");
        
        if (ProcessSteps != null && ProcessSteps.Count > 0)
        {
            System.Diagnostics.Debug.WriteLine("=== 工序列表 ===");
            foreach (var step in ProcessSteps)
            {
                System.Diagnostics.Debug.WriteLine($"工序: {step.ProcessStepName}, ID: {step.Id}, StepOrder: {step.StepOrder}");
            }
        }
        
        System.Diagnostics.Debug.WriteLine("=== 字段值打印完成 ===");
    }
    
    /// <summary>
    /// 强制设置所有字段值
    /// </summary>
    public void ForceSetAllFields()
    {
        System.Diagnostics.Debug.WriteLine("=== 开始强制设置所有字段 ===");
        
        // 强制设置ProcessRouteId
        if (string.IsNullOrEmpty(ProcessRouteId) && !string.IsNullOrEmpty(SelectedProcessRouteCode))
        {
            ProcessRouteId = SelectedProcessRouteCode;
            System.Diagnostics.Debug.WriteLine($"强制设置ProcessRouteId: {ProcessRouteId}");
        }
        
        // 强制设置ProcessStepId
        if (ProcessStepId == 0 && SelectedProcessStep != null)
        {
            if (!string.IsNullOrEmpty(SelectedProcessStep.Id) && int.TryParse(SelectedProcessStep.Id, out int processStepId))
            {
                ProcessStepId = processStepId;
                System.Diagnostics.Debug.WriteLine($"强制设置ProcessStepId: {ProcessStepId}");
            }
            else
            {
                ProcessStepId = SelectedProcessStep.StepOrder;
                System.Diagnostics.Debug.WriteLine($"强制设置ProcessStepId使用StepOrder: {ProcessStepId}");
            }
        }
        
        // 强制设置ProductId（如果需要）
        if (ProductId == 0 && !string.IsNullOrEmpty(ProductCode))
        {
            // 这里可以添加从ProductCode获取ProductId的逻辑
            System.Diagnostics.Debug.WriteLine("ProductId为0，需要从ProductCode获取");
        }
        
        System.Diagnostics.Debug.WriteLine("=== 强制设置完成 ===");
        DebugPrintAllFields();
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _httpClient?.Dispose();
    }
    }

/// <summary>
/// BOM创建数据传输对象
/// </summary>
public class BomCreateDto
{
    public string BomCode { get; set; } = string.Empty;
    public string BomVersion { get; set; } = string.Empty;
    public int ProductId { get; set; } // 添加ProductId字段
    public string ProductCode { get; set; } = string.Empty;
    public string Specification { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public int DailyOutput { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public int State { get; set; }
    public long ProcessRouteId { get; set; } // 添加ProcessRouteId字段
    public int ProcessStepId { get; set; } // 添加ProcessStepId字段
    public int[] MaterialIds { get; set; } = new int[0];
    public int[] MaterialIdsw { get; set; } = new int[0];
}

    /// <summary>
    /// 物料项目模型
    /// </summary>
    public class MaterialItem : INotifyPropertyChanged
    {
        private bool _isSelected;
        private int _index;
        private string _materialCode = "";
        private string _materialName = "";
        private string _specification = "";
        private string _unit = "";
        private decimal _usageAmount = 0;
        private decimal _usageRatio = 0;

        public MaterialItem()
        {
            System.Diagnostics.Debug.WriteLine("MaterialItem构造函数被调用");
        }

        public int Index
        {
            get => _index;
            set
            {
                if (_index != value)
                {
                    _index = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 物料编号
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string MaterialCode
        {
            get
            {
                System.Diagnostics.Debug.WriteLine($"MaterialCode.get 被调用，返回值: '{_materialCode}'");
                return _materialCode;
            }
            set
            {
                System.Diagnostics.Debug.WriteLine($"MaterialCode.set 被调用，新值: '{value}', 旧值: '{_materialCode}'");
                if (_materialCode != value)
                {
                    _materialCode = value;
                    System.Diagnostics.Debug.WriteLine($"MaterialCode 已更新为: '{_materialCode}'，触发PropertyChanged");
                    OnPropertyChanged();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("MaterialCode 值未变化，不触发PropertyChanged");
                }
            }
        }

        /// <summary>
        /// 物料名称（必填）
        /// </summary>
        public string MaterialName
        {
            get
            {
                System.Diagnostics.Debug.WriteLine($"MaterialName.get 被调用，返回值: '{_materialName}'");
                return _materialName;
            }
            set
            {
                System.Diagnostics.Debug.WriteLine($"MaterialName.set 被调用，新值: '{value}', 旧值: '{_materialName}'");
                if (_materialName != value)
                {
                    _materialName = value;
                    System.Diagnostics.Debug.WriteLine($"MaterialName 已更新为: '{_materialName}'，触发PropertyChanged");
                    OnPropertyChanged();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("MaterialName 值未变化，不触发PropertyChanged");
                }
            }
        }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification
        {
            get => _specification;
            set
            {
                if (_specification != value)
                {
                    _specification = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit
        {
            get => _unit;
            set
            {
                if (_unit != value)
                {
                    _unit = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 使用量
        /// </summary>
        public decimal UsageAmount
        {
            get => _usageAmount;
            set
            {
                if (_usageAmount != value)
                {
                    _usageAmount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 用料比例
        /// </summary>
        public decimal UsageRatio
        {
            get => _usageRatio;
            set
            {
                if (_usageRatio != value)
                {
                    _usageRatio = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否被选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
