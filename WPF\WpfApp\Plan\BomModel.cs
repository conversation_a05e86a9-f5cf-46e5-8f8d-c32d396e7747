using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace WpfApp.Plan
{
    /// <summary>
    /// BOM数据模型
    /// </summary>
    public class BomModel
    {

        public string Id { get; set; }
        public string BomCode { get; set; }
        public bool IsSystemCode { get; set; }
        public bool IsDefaultBom { get; set; }
        public string BomVersion { get; set; }
        public int ProductId { get; set; }  // 改为int类型以匹配API返回
        public string ProductName { get; set; } // 添加产品名称属性
        public string ProductCode { get; set; }
        public string Specification { get; set; }
        public string Unit { get; set; }
        public int DailyOutput { get; set; }
        public string Remarks { get; set; }
        public string ProcessRouteId { get; set; }
        public bool IsDelete { get; set; }

        [JsonConverter(typeof(DateTimeConverter))]
        public DateTime CreateTime { get; set; }

        [JsonConverter(typeof(NullableDateTimeConverter))]
        public DateTime? UpdateTime { get; set; }

        public string CreateUserId { get; set; }
        public string CreateUserName { get; set; }
        public string UpdateUserId { get; set; }
        public string UpdateUserName { get; set; }
    }

    /// <summary>
    /// 自定义DateTime转换器，处理字符串格式的日期时间
    /// </summary>
    public class DateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                var dateString = reader.GetString();
                if (DateTime.TryParse(dateString, out var result))
                {
                    return result;
                }
            }
            return default(DateTime);
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString("yyyy-MM-dd HH:mm:ss"));
        }
    }

    /// <summary>
    /// 自定义可空DateTime转换器
    /// </summary>
    public class NullableDateTimeConverter : JsonConverter<DateTime?>
    {
        public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
            {
                return null;
            }

            if (reader.TokenType == JsonTokenType.String)
            {
                var dateString = reader.GetString();
                if (string.IsNullOrEmpty(dateString))
                {
                    return null;
                }

                if (DateTime.TryParse(dateString, out var result))
                {
                    return result;
                }
            }
            return null;
        }

        public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
            {
                writer.WriteStringValue(value.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            else
            {
                writer.WriteNullValue();
            }
        }
    }
}