using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.IO;
using System.Threading.Tasks;
using System.Linq;

namespace WpfApp.Plan
{
    public class EditPlanViewModel : IDisposable, INotifyPropertyChanged
    {
        private readonly HttpClient _httpClient;
        private readonly Action<bool> _goBackAction; // 返回到列表页并可选择是否刷新数据
        private readonly Action _backAction; // 简化的返回操作
        private bool _isLoading;
        private List<SelectOption> _sourceTypes;
        private SelectOption _selectedSourceType;

        public PlanModel PlanModel { get; set; }
        public bool IsNewPlan { get; set; }

        public string PageTitle => IsNewPlan ? "新增计划" : "编辑计划";
        
        public bool IsLoading 
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                // 如果有PropertyChanged事件，这里需要通知UI
            }
        }
        
        public List<SelectOption> SourceTypes
        {
            get => _sourceTypes;
            set
            {
                _sourceTypes = value;
                // 如果有PropertyChanged事件，这里需要通知UI
            }
        }
        
        public SelectOption SelectedSourceType
        {
            get => _selectedSourceType;
            set
            {
                _selectedSourceType = value;
                if (PlanModel != null && value != null)
                {
                    PlanModel.SourceId = value.Id;
                    PlanModel.SourceName = value.Name;
                }
                // 如果有PropertyChanged事件，这里需要通知UI
            }
        }
        
        // 用于显示的附件文件名
        public string AttachmentDisplayName
        {
            get
            {
                if (string.IsNullOrEmpty(PlanModel?.PlanAttachment))
                    return "";

                // 从URL中提取文件名
                try
                {
                    var uri = new Uri(PlanModel.PlanAttachment);
                    var fileName = Path.GetFileName(uri.LocalPath);
                    
                    // 如果提取的文件名为空，尝试从URL路径中提取
                    if (string.IsNullOrEmpty(fileName))
                    {
                        var pathSegments = uri.AbsolutePath.Split('/');
                        fileName = pathSegments.LastOrDefault();
                    }
                    
                    return string.IsNullOrEmpty(fileName) ? "未知文件" : fileName;
                }
                catch
                {
                    // 如果不是有效URL，尝试直接提取文件名
                    var path = PlanModel.PlanAttachment;
                    if (path.Contains('/'))
                    {
                        return Path.GetFileName(path);
                    }
                    return PlanModel.PlanAttachment;
                }
            }
        }
        
        // 命令
        public ICommand SavePlanCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        public ICommand BackCommand { get; private set; }
        public ICommand SelectProductCommand { get; private set; }
        public ICommand SelectOrderCommand { get; private set; }
        public ICommand SelectBomCommand { get; private set; }
        public ICommand UploadAttachmentCommand { get; private set; }
        
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        
        // 原有构造函数 - 支持传递返回并刷新的操作
        public EditPlanViewModel(Action<bool> goBackAction, PlanModel planModel = null)
        {
            _httpClient = new HttpClient();
            // 不设置BaseAddress，使用完整URL
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("text/plain"));
            
            // 添加认证头
            if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
            }
            
            // 添加请求来源标识
            _httpClient.DefaultRequestHeaders.Add("request-from", "swagger");
            
            _goBackAction = goBackAction;
            
            IsNewPlan = planModel == null;
            PlanModel = planModel ?? new PlanModel
            {
                PlanStartTime = DateTime.Now,
                PlanEndTime = DateTime.Now.AddDays(7),
                DemandTime = DateTime.Now.AddDays(10),
                PlanCode = GeneratePlanCode(),
                PlanStatus = 0,
                PlanStatusName = "未下达"
            };
            
            InitializeCommands();
            InitializeOptions();
        }
        
        // 新构造函数 - 支持简单的返回操作
        public EditPlanViewModel(bool isNewPlan = true, Action backAction = null)
        {
            _httpClient = new HttpClient();
            // 不设置BaseAddress，使用完整URL
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("text/plain"));
            
            // 添加认证头
            if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
            }
            
            // 添加请求来源标识
            _httpClient.DefaultRequestHeaders.Add("request-from", "swagger");
            
            _backAction = backAction;
            IsNewPlan = isNewPlan;
            
            PlanModel = new PlanModel
            {
                PlanStartTime = DateTime.Now,
                PlanEndTime = DateTime.Now.AddDays(7),
                DemandTime = DateTime.Now.AddDays(10),
                PlanCode = GeneratePlanCode(),
                PlanStatus = 0,
                PlanStatusName = "未下达"
            };
            
            InitializeCommands();
            InitializeOptions();
        }
        
        private void InitializeCommands()
        {
            SavePlanCommand = new RelayCommand(SavePlan);
            CancelCommand = new RelayCommand(Cancel);
            BackCommand = new RelayCommand(GoBack);
            SelectProductCommand = new RelayCommand(SelectProduct);
            SelectOrderCommand = new RelayCommand(SelectOrder);
            SelectBomCommand = new RelayCommand(SelectBom);
            UploadAttachmentCommand = new RelayCommand(UploadAttachment);
        }
        
        private async void InitializeOptions()
        {
            // 从API获取来源类型数据
            await LoadSourceTypesFromApi();
        }
        
        // 加载计划详情（用于编辑模式）
        public async Task LoadPlanDetails(string planId)
        {
            if (string.IsNullOrEmpty(planId))
            {
                System.Diagnostics.Debug.WriteLine("计划ID为空，无法加载详情");
                return;
            }

            IsLoading = true;

            try
            {
                var url = $"http://localhost:5005/api/productionPlan/productionPlanDetail?id={planId}";
                System.Diagnostics.Debug.WriteLine($"=== 开始获取计划详情 ===");
                System.Diagnostics.Debug.WriteLine($"计划ID: {planId}");
                System.Diagnostics.Debug.WriteLine($"请求URL: {url}");

                var response = await _httpClient.GetAsync(url);
                System.Diagnostics.Debug.WriteLine($"响应状态码: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"计划详情响应: {json}");

                    // 配置JSON序列化选项
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        Converters = { new JsonStringDateTimeConverter() }
                    };

                    var result = JsonSerializer.Deserialize<PlanDetailResponse>(json, options);

                    if (result?.Code == 200 && result.Result != null)
                    {
                        // 保存当前的数据
                        var currentPlanModel = PlanModel;
                        
                        // 更新PlanModel
                        PlanModel = result.Result;
                        
                        // 如果当前有数据，则合并数据（保留当前数据，用服务器数据补充缺失的字段）
                        if (currentPlanModel != null)
                        {
                            System.Diagnostics.Debug.WriteLine("合并现有数据和服务器数据");
                            System.Diagnostics.Debug.WriteLine($"当前数据: PlanName={currentPlanModel.PlanName}, ProductName={currentPlanModel.ProductName}, ProductType={currentPlanModel.ProductType}");
                            System.Diagnostics.Debug.WriteLine($"服务器数据: PlanName={PlanModel.PlanName}, ProductName={PlanModel.ProductName}, ProductType={PlanModel.ProductType}");
                            
                            // 保留当前数据，用服务器数据补充缺失的字段
                            if (string.IsNullOrEmpty(currentPlanModel.PlanName) && !string.IsNullOrEmpty(PlanModel.PlanName))
                                currentPlanModel.PlanName = PlanModel.PlanName;
                            if (string.IsNullOrEmpty(currentPlanModel.ProductName) && !string.IsNullOrEmpty(PlanModel.ProductName))
                                currentPlanModel.ProductName = PlanModel.ProductName;
                            if (string.IsNullOrEmpty(currentPlanModel.ProductCode) && !string.IsNullOrEmpty(PlanModel.ProductCode))
                                currentPlanModel.ProductCode = PlanModel.ProductCode;
                            if (string.IsNullOrEmpty(currentPlanModel.ProductType) && !string.IsNullOrEmpty(PlanModel.ProductType))
                                currentPlanModel.ProductType = PlanModel.ProductType;
                            if (string.IsNullOrEmpty(currentPlanModel.ProductSpecification) && !string.IsNullOrEmpty(PlanModel.ProductSpecification))
                                currentPlanModel.ProductSpecification = PlanModel.ProductSpecification;
                            if (string.IsNullOrEmpty(currentPlanModel.ProductUnit) && !string.IsNullOrEmpty(PlanModel.ProductUnit))
                                currentPlanModel.ProductUnit = PlanModel.ProductUnit;
                            
                            // 使用合并后的数据
                            PlanModel = currentPlanModel;
                        }

                        // 根据来源ID设置选中的来源类型
                        if (SourceTypes != null && !string.IsNullOrEmpty(PlanModel.SourceId))
                        {
                            SelectedSourceType = SourceTypes.FirstOrDefault(s => s.Id == PlanModel.SourceId);
                        }

                        // 通知UI更新所有绑定到PlanModel的属性
                        OnPropertyChanged(nameof(PlanModel));
                        OnPropertyChanged(nameof(AttachmentDisplayName));

                        System.Diagnostics.Debug.WriteLine($"计划详情加载成功: {PlanModel.PlanName}");
                        System.Diagnostics.Debug.WriteLine($"产品类型: {PlanModel.ProductType}");
                        System.Diagnostics.Debug.WriteLine($"产品名称: {PlanModel.ProductName}");
                        System.Diagnostics.Debug.WriteLine($"产品编号: {PlanModel.ProductCode}");
                        System.Diagnostics.Debug.WriteLine($"规格: {PlanModel.Specification}");
                        System.Diagnostics.Debug.WriteLine($"单位: {PlanModel.Unit}");
                        
                        // 移除成功提示框
                    }
                    else
                    {
                        var errorMsg = $"API返回错误: Code={result?.Code}, Message={result?.Message}";
                        System.Diagnostics.Debug.WriteLine(errorMsg);
                        MessageBox.Show($"获取计划详情失败: {result?.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorMsg = $"HTTP错误: {response.StatusCode} - {response.ReasonPhrase}";
                    System.Diagnostics.Debug.WriteLine($"{errorMsg}\n错误内容: {errorContent}");
                    MessageBox.Show($"获取计划详情失败: {response.StatusCode} - {response.ReasonPhrase}\n详细信息: {errorContent}", "HTTP错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (HttpRequestException ex)
            {
                var errorMsg = $"网络请求异常: {ex.Message}";
                System.Diagnostics.Debug.WriteLine(errorMsg);
                MessageBox.Show($"网络请求失败，请检查服务器是否运行: {ex.Message}", "网络错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch (Exception ex)
            {
                var errorMsg = $"获取计划详情时发生异常: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"{errorMsg}\n异常堆栈: {ex.StackTrace}");
                MessageBox.Show(errorMsg, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                System.Diagnostics.Debug.WriteLine("=== 计划详情加载完成 ===");
            }
        }

        private async Task LoadSourceTypesFromApi()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始调用API获取来源类型数据 ===");
                
                // 确保SourceTypes不为null
                if (SourceTypes == null)
                {
                    SourceTypes = new List<SelectOption>();
                    System.Diagnostics.Debug.WriteLine("SourceTypes为null，已创建新实例");
                }
                
                // 调用API获取来源类型列表
                var response = await _httpClient.GetAsync("http://localhost:5005/api/productionPlan/sourceTypeList");
                
                System.Diagnostics.Debug.WriteLine($"API响应状态码: {response.StatusCode}");
                
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"API响应内容: {json}");
                    
                    var result = JsonSerializer.Deserialize<SourceTypeResponse>(json);
                    
                    if (result?.Result != null)
                    {
                        // 清空现有数据
                        SourceTypes.Clear();
                        
                        // 添加从API获取的数据
                        foreach (var sourceType in result.Result)
                        {
                            if (sourceType != null && !string.IsNullOrEmpty(sourceType.SourceName))
                            {
                                SourceTypes.Add(new SelectOption 
                                { 
                                    Id = sourceType.Id ?? "", 
                                    Name = sourceType.SourceName 
                                });
                                System.Diagnostics.Debug.WriteLine($"添加来源类型: {sourceType.SourceName} (ID: {sourceType.Id})");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"跳过无效的来源类型数据: {sourceType}");
                            }
                        }
                        
                        // 设置默认选中项
                        if (SourceTypes.Count > 0)
                        {
                            SelectedSourceType = SourceTypes[0];
                            
                            // 确保PlanModel的SourceId被设置
                            if (PlanModel != null)
                            {
                                PlanModel.SourceId = SelectedSourceType.Id;
                                PlanModel.SourceName = SelectedSourceType.Name;
                            }
                            
                            System.Diagnostics.Debug.WriteLine($"设置默认选中项: {SelectedSourceType.Name}");
                        }
                        
                        System.Diagnostics.Debug.WriteLine($"=== 成功从API加载来源类型数据，共 {SourceTypes.Count} 条 ===");
                        MessageBox.Show($"成功从API加载了 {SourceTypes.Count} 个来源类型", "API调用成功", MessageBoxButton.OK, MessageBoxImage.Information);
                        return; // 成功加载，直接返回
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("API响应中没有Result数据");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"获取来源类型失败: {response.StatusCode}");
                    var errorContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"错误响应内容: {errorContent}");
                    MessageBox.Show($"API调用失败，状态码: {response.StatusCode}", "API调用失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                
                // 如果API调用失败，使用默认数据
                System.Diagnostics.Debug.WriteLine("=== 使用默认来源类型数据 ===");
                InitializeDefaultSourceTypes();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取来源类型时发生错误: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"API调用异常: {ex.Message}", "API调用异常", MessageBoxButton.OK, MessageBoxImage.Error);
                // 如果发生异常，使用默认数据
                InitializeDefaultSourceTypes();
            }
        }
        
        private void InitializeDefaultSourceTypes()
        {
            try
            {
                // 确保SourceTypes不为null
                if (SourceTypes == null)
                {
                    SourceTypes = new List<SelectOption>();
                }
                
                // 清空现有数据
                SourceTypes.Clear();
                
                // 添加默认的来源类型选项
                SourceTypes.Add(new SelectOption { Id = "700723681652823", Name = "销售订单" });
                SourceTypes.Add(new SelectOption { Id = "700723681653476", Name = "库存备货" });
                
                // 设置默认选中项
                if (SourceTypes.Count > 0)
                {
                    SelectedSourceType = SourceTypes[0];
                    
                    // 确保PlanModel的SourceId被设置
                    if (PlanModel != null)
                    {
                        PlanModel.SourceId = SelectedSourceType.Id;
                        PlanModel.SourceName = SelectedSourceType.Name;
                    }
                }
                
                System.Diagnostics.Debug.WriteLine("使用默认来源类型数据");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化默认来源类型时发生错误: {ex.Message}");
            }
        }
        
        private string GeneratePlanCode()
        {
            // 生成计划编号：JHBH + 年月日 + 4位随机数
            string dateStr = DateTime.Now.ToString("yyyyMMdd");
            Random random = new Random();
            string randomNum = random.Next(1000, 9999).ToString();
            return $"JHBH{dateStr}{randomNum}";
        }
        
        public async void SavePlan()
        {
            // 验证必填项
            if (string.IsNullOrEmpty(PlanModel.PlanName))
            {
                MessageBox.Show("请输入计划名称", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (string.IsNullOrEmpty(PlanModel.SourceId))
            {
                MessageBox.Show("请选择来源类型", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (PlanModel.PlanNumber <= 0)
            {
                MessageBox.Show("请输入有效的计划数量", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            IsLoading = true;
            
            try
            {
                // 移除健康检查，直接进行API调用
                
                // 准备请求数据，按照API文档格式
                var requestData = new
                {
                    planName = PlanModel.PlanName ?? "",
                    workOrderNumber = 0, // 默认值
                    sourceId = PlanModel.SourceId ?? "0",
                    productId = PlanModel.ProductId ?? "",
                    planNumber = PlanModel.PlanNumber,
                    planStartTime = PlanModel.PlanStartTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    planEndTime = PlanModel.PlanEndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.AddDays(7).ToString("yyyy-MM-dd HH:mm:ss"),
                    demandTime = PlanModel.DemandTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.AddDays(7).ToString("yyyy-MM-dd HH:mm:ss"),
                    planRemark = PlanModel.PlanRemark ?? "",
                    planAttachment = PlanModel.PlanAttachment ?? "",
                    bomId = PlanModel.BomId ?? "0",
                    orderId = PlanModel.OrderId ?? "0"
                };
                
                string json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json-patch+json");
                
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"请求URL: http://localhost:5005/api/productionPlan/productionPlan");
                System.Diagnostics.Debug.WriteLine($"请求数据: {json}");
                
                HttpResponseMessage response;
                if (IsNewPlan)
                {
                    // 新增计划
                    response = await _httpClient.PostAsync("http://localhost:5005/api/productionPlan/productionPlan", content);
                    System.Diagnostics.Debug.WriteLine($"响应状态码: {response.StatusCode}");
                    System.Diagnostics.Debug.WriteLine($"响应内容: {await response.Content.ReadAsStringAsync()}");
                }
                else
                {
                    // 更新计划 - 使用正确的API格式
                    var updateUrl = $"http://localhost:5005/api/productionPlan/productionPlan";
                    System.Diagnostics.Debug.WriteLine($"更新计划URL: {updateUrl}");
                    System.Diagnostics.Debug.WriteLine($"计划ID: {PlanModel.Id}");
                    
                    // 在请求数据中包含ID
                    var updateRequestData = new
                    {
                        id = PlanModel.Id,
                        planName = PlanModel.PlanName ?? "",
                        workOrderNumber = 0, // 默认值
                        sourceId = PlanModel.SourceId ?? "0",
                        productId = PlanModel.ProductId ?? "",
                        planNumber = PlanModel.PlanNumber,
                        planStartTime = PlanModel.PlanStartTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        planEndTime = PlanModel.PlanEndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.AddDays(7).ToString("yyyy-MM-dd HH:mm:ss"),
                        demandTime = PlanModel.DemandTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.AddDays(7).ToString("yyyy-MM-dd HH:mm:ss"),
                        planRemark = PlanModel.PlanRemark ?? "",
                        planAttachment = PlanModel.PlanAttachment ?? "",
                        bomId = PlanModel.BomId ?? "0",
                        orderId = PlanModel.OrderId ?? "0"
                    };
                    
                    string updateJson = JsonSerializer.Serialize(updateRequestData);
                    var updateContent = new StringContent(updateJson, Encoding.UTF8, "application/json-patch+json");
                    
                    System.Diagnostics.Debug.WriteLine($"更新请求数据: {updateJson}");
                    response = await _httpClient.PutAsync(updateUrl, updateContent);
                    System.Diagnostics.Debug.WriteLine($"更新响应状态码: {response.StatusCode}");
                    System.Diagnostics.Debug.WriteLine($"更新响应内容: {await response.Content.ReadAsStringAsync()}");
                }
                
                if (response.IsSuccessStatusCode)
                {
                    System.Diagnostics.Debug.WriteLine("保存成功，准备跳转");
                    System.Diagnostics.Debug.WriteLine($"_goBackAction: {_goBackAction != null}");
                    System.Diagnostics.Debug.WriteLine($"_backAction: {_backAction != null}");
                    
                    // 先显示保存成功提示框
                    MessageBox.Show("保存成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // 用户点击确定后跳转
                    if (_goBackAction != null)
                    {
                        System.Diagnostics.Debug.WriteLine("调用_goBackAction");
                        _goBackAction(true); // 返回并刷新
                    }
                    else if (_backAction != null)
                    {
                        System.Diagnostics.Debug.WriteLine("调用_backAction");
                        _backAction(); // 简单返回
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("没有设置回调函数");
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    MessageBox.Show($"保存失败: {response.ReasonPhrase}\n详细信息: {errorContent}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        public void Cancel()
        {
            if (_goBackAction != null)
            {
                _goBackAction(false); // 返回但不刷新
            }
            else if (_backAction != null)
            {
                _backAction(); // 简单返回
            }
        }
        
        public void GoBack()
        {
            Cancel(); // 复用取消逻辑
        }
        
        public void SelectProduct()
        {
            IsLoading = true;

            try
            {
                // 创建产品选择窗口
                var productSelectionWindow = new ProductSelectionWindow();

                // 显示为对话框并获取结果
                if (productSelectionWindow.ShowDialog() == true && productSelectionWindow.SelectedProduct != null)
                {
                    // 保存选择的产品信息
                    var selectedProduct = productSelectionWindow.SelectedProduct;
                    PlanModel.ProductId = selectedProduct.Id;  // 不再需要ToString()转换
                    PlanModel.ProductName = selectedProduct.ProductName;
                    PlanModel.ProductCode = selectedProduct.ProductCode;
                    PlanModel.Specification = selectedProduct.Specification;
                    PlanModel.Unit = selectedProduct.Unit;
                    PlanModel.ProductType = selectedProduct.ProductType;

                    // 清空BOM信息，因为产品变了
                    PlanModel.BomId = null;
                    PlanModel.BomCode = null;
                    PlanModel.BomVersion = null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择产品时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        public void SelectOrder()
        {
            // 实现订单选择功能
            MessageBox.Show("请实现订单选择功能", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        public void SelectBom()
        {
            IsLoading = true;
            
            try
            {
                // 创建BOM选择窗口
                var bomSelectionWindow = new BomSelectionWindow(
                    PlanModel.ProductId,  // 已经是int类型，不需要转换
                    PlanModel.ProductName,
                    PlanModel.ProductCode,
                    PlanModel.Specification,
                    PlanModel.Unit
                );
                
                // 显示为对话框并获取结果
                if (bomSelectionWindow.ShowDialog() == true && bomSelectionWindow.SelectedBom != null)
                {
                    // 保存选择的BOM信息
                    PlanModel.BomId = bomSelectionWindow.SelectedBom.Id;
                    PlanModel.BomCode = bomSelectionWindow.SelectedBom.BomCode;
                    PlanModel.BomVersion = bomSelectionWindow.SelectedBom.BomVersion;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择BOM时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        public async void UploadAttachment()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择附件文件",
                Filter = "支持的文件类型|*.docx;*.xls;*.xlsx;*.pdf;*.rar;*.zip;*.png;*.jpg;*.jpeg|所有文件|*.*",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"用户选择了文件: {openFileDialog.FileName}");

                    // 上传文件到服务器
                    var uploadResult = await UploadFileToServer(openFileDialog.FileName);

                    if (uploadResult != null && uploadResult.Code == 200)
                    {
                        // 修正URL格式，确保与API返回格式一致
                        string fileUrl = uploadResult.Result.Url;
                        
                        // 如果URL不是以http开头，添加基础URL
                        if (!fileUrl.StartsWith("http://") && !fileUrl.StartsWith("https://"))
                        {
                            fileUrl = $"http://localhost:5005/{fileUrl}";
                        }
                        
                        // 保存修正后的URL到计划中
                        PlanModel.PlanAttachment = fileUrl;
                        OnPropertyChanged(nameof(AttachmentDisplayName)); // 通知界面更新显示名称

                        System.Diagnostics.Debug.WriteLine($"文件上传成功，文件名: {uploadResult.Result.FileName}");
                        System.Diagnostics.Debug.WriteLine($"原始URL: {uploadResult.Result.Url}");
                        System.Diagnostics.Debug.WriteLine($"修正后URL: {fileUrl}");

                        MessageBox.Show($"文件上传成功！\n文件名: {uploadResult.Result.FileName}\n文件大小: {uploadResult.Result.SizeKb}KB",
                                      "上传成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"文件上传失败: Code={uploadResult?.Code}, Message={uploadResult?.Message}");
                        MessageBox.Show($"文件上传失败: {uploadResult?.Message ?? "未知错误"}",
                                      "上传失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"文件上传异常: {ex.Message}\n{ex.StackTrace}");
                    MessageBox.Show($"文件上传过程中发生错误: {ex.Message}",
                                  "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private async Task<FileUploadResponse> UploadFileToServer(string filePath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始上传文件: {filePath}");

                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(60); // 设置超时时间为60秒

                // 设置请求头
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                    System.Diagnostics.Debug.WriteLine("已添加Authorization头");
                }

                // 添加其他可能需要的请求头
                client.DefaultRequestHeaders.Add("Accept", "*/*");
                client.DefaultRequestHeaders.Add("request-from", "swagger");

                // 创建MultipartFormDataContent - 模拟Swagger的multipart/form-data格式
                using var formData = new MultipartFormDataContent();

                // 读取文件内容
                var fileBytes = await File.ReadAllBytesAsync(filePath);
                var fileName = Path.GetFileName(filePath);

                System.Diagnostics.Debug.WriteLine($"文件名: {fileName}, 文件大小: {fileBytes.Length} bytes");

                // 添加文件到表单数据 - 根据API文档使用正确的参数名
                var fileContent = new ByteArrayContent(fileBytes);
                
                // 根据文件扩展名设置正确的MIME类型
                string mimeType = GetMimeType(fileName);
                fileContent.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse(mimeType);
                
                System.Diagnostics.Debug.WriteLine($"设置文件MIME类型: {mimeType}");
                
                formData.Add(fileContent, "file", fileName);

                System.Diagnostics.Debug.WriteLine("准备发送文件上传请求...");

                // 发送POST请求到文件上传接口
                var response = await client.PostAsync("http://localhost:5005/api/productionPlanUpload/uploadAttachment", formData);

                System.Diagnostics.Debug.WriteLine($"文件上传响应状态码: {response.StatusCode}");
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"文件上传响应内容: {responseContent}");

                    var uploadResponse = JsonSerializer.Deserialize<FileUploadResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    System.Diagnostics.Debug.WriteLine($"解析后的上传响应: Code={uploadResponse?.Code}, Message={uploadResponse?.Message}");

                    return uploadResponse;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"文件上传失败: {response.StatusCode}, 错误内容: {errorContent}");
                    MessageBox.Show($"HTTP错误: {response.StatusCode}\n详细信息: {errorContent}", "上传失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    return null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"网络错误: {ex.Message}", "上传失败", MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }
        
        // 根据文件扩展名获取MIME类型
        private string GetMimeType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".pdf" => "application/pdf",
                ".png" => "image/png",
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".zip" => "application/zip",
                ".rar" => "application/x-rar-compressed",
                _ => "application/octet-stream"
            };
        }
        
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
    
    // 文件上传响应模型
    public class FileUploadResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public FileUploadResult Result { get; set; }
        public object Extras { get; set; }
        public string Time { get; set; }
    }
    
    public class FileUploadResult
    {
        public string Provider { get; set; }
        public string BucketName { get; set; }
        public string FileName { get; set; }
        public string Suffix { get; set; }
        public string FilePath { get; set; }
        public string SizeKb { get; set; }
        public object SizeInfo { get; set; }
        public string Url { get; set; }
        public string FileMd5 { get; set; }
        public string FileType { get; set; }
        public object FileAlias { get; set; }
        public bool IsPublic { get; set; }
        public object DataId { get; set; }
        public string TenantId { get; set; }
        public string OrgId { get; set; }
        public string CreateTime { get; set; }
        public object UpdateTime { get; set; }
        public string CreateUserId { get; set; }
        public string CreateUserName { get; set; }
        public object UpdateUserId { get; set; }
        public object UpdateUserName { get; set; }
        public string Id { get; set; }
    }

    // 计划详情响应模型
    public class PlanDetailResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public PlanModel Result { get; set; }
        public object Extras { get; set; }
        public string Time { get; set; }
    }
}