﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media;

namespace WpfApp.ProductionSchedu
{
    public class AddTaskViewModel : INotifyCollectionChanged
    {
        private string _taskCode = "";
        private string _taskName = "";
        private bool _isSystemGenerated = true;
        private SiteModel _selectedSite;
        private string _siteCode = "";
        private string _taskColorHex = "";
        private int _planQuantity = 0;
        private DateTime _startTime = DateTime.Now;
        private DateTime _endTime = DateTime.Now.AddDays(1);
        public long WorkOrderId { get; set; }

        public bool IsEditMode { get; set; } = false;
        public string TaskId { get; set; }
        public string ProcessRouteId { get; set; }
        public string ProcessStepId { get; set; }


        public string TaskCode
        {
            get => _taskCode;
            set { _taskCode = value; OnPropertyChanged(nameof(TaskCode)); }
        }

        public string TaskName
        {
            get => _taskName;
            set { _taskName = value; OnPropertyChanged(nameof(TaskName)); }
        }

        public bool IsSystemGenerated
        {
            get => _isSystemGenerated;
            set
            {
                _isSystemGenerated = value;
                OnPropertyChanged(nameof(IsSystemGenerated));
                OnPropertyChanged(nameof(IsManualTaskCode));

                // 当切换到系统编号时，自动生成编号
                if (value)
                {
                    TaskCode = GenerateTaskCode();
                }
                else
                {
                    // 切换到手动模式时清空编号
                    TaskCode = "";
                }
            }
        }

        public bool IsManualTaskCode => !IsSystemGenerated;


        public ICommand CancelCommand { get; }
        public ICommand ConfirmCommand { get; }

        public event Action<bool> DialogClosed;
        public event PropertyChangedEventHandler PropertyChanged;

        

        public AddTaskViewModel(long workOrderId)
        {
            WorkOrderId = workOrderId;
            CancelCommand = new RelayCommand(_ =>
            {
                DialogClosed?.Invoke(false);
            });
            ConfirmCommand = new RelayCommand(async _=>await ConfirmAsync() );
            TaskCode = GenerateTaskCode();
            LoadSites();
        }

        // 生成任务编号的方法
        private string GenerateTaskCode()
        {
            // 生成格式：TASK + 年月日 + 4位随机数
            var dateStr = DateTime.Now.ToString("yyyyMMdd");
            var random = new Random();
            var randomNum = random.Next(1000, 9999);
            return $"TASK{dateStr}{randomNum}";
        }

        public int PlanQuantity
        {
            get => _planQuantity;
            set { _planQuantity = value; OnPropertyChanged(nameof(PlanQuantity)); }
        }

        public DateTime StartTime
        {
            get => _startTime;
            set { _startTime = value; OnPropertyChanged(nameof(StartTime)); }
        }

        public DateTime EndTime
        {
            get => _endTime;
            set { _endTime = value; OnPropertyChanged(nameof(EndTime)); }
        }

        private async Task ConfirmAsync()
        {
            try
            {
                // 验证必填字段213
                if (string.IsNullOrEmpty(TaskName))
                {
                    System.Windows.MessageBox.Show("请输入任务名称", "提示");
                    return;
                }

                if (SelectedSite == null)
                {
                    System.Windows.MessageBox.Show("请选择站点", "提示");
                    return;
                }
                if(WorkOrderId<=0)
                {
                    System.Windows.MessageBox.Show("工单id无效", "提示");
                    return;
                }

                // 添加更详细的调试信息
                System.Diagnostics.Debug.WriteLine($"SelectedSite对象: {SelectedSite}");
                System.Diagnostics.Debug.WriteLine($"SelectedSite.SiteId: '{SelectedSite.SitesId}'");
                System.Diagnostics.Debug.WriteLine($"SelectedSite.SiteName: '{SelectedSite.SiteName}'");
                System.Diagnostics.Debug.WriteLine($"SiteId是否为空: {string.IsNullOrEmpty(SelectedSite.SitesId)}");

                if (string.IsNullOrEmpty(SelectedSite.SitesId))
                {
                    System.Windows.MessageBox.Show("站点ID为空，请重新选择站点", "提示");
                    return;
                }

                if (PlanQuantity <= 0)
                {
                    System.Windows.MessageBox.Show("请输入有效的计划数量", "提示");
                    return;
                }

                // 验证工艺路线和工序参数
                if (string.IsNullOrEmpty(ProcessRouteId))
                {
                    System.Windows.MessageBox.Show("工艺路线ID不能为空", "提示");
                    return;
                }

                if (string.IsNullOrEmpty(ProcessStepId))
                {
                    System.Windows.MessageBox.Show("工序ID不能为空", "提示");
                    return;
                }

                // 如果是手动编号模式，验证任务编号
                if (!IsSystemGenerated && string.IsNullOrEmpty(TaskCode))
                {
                    System.Windows.MessageBox.Show("请输入任务编号", "提示");
                    return;
                }

                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                // 确保使用正确的站点ID
                var sitesId = SelectedSite.SitesId;
                System.Diagnostics.Debug.WriteLine($"准备提交的站点ID: '{sitesId}'");
                if (IsEditMode)
                {
                    // 编辑模式 - 使用PUT请求
                    // 先验证TaskId
                    if (string.IsNullOrEmpty(TaskId))
                    {
                        System.Windows.MessageBox.Show("任务ID为空，无法更新", "错误");
                        return;
                    }

                    var taskData = new
                    {
                        taskNumber = TaskCode,
                        taskName = TaskName,
                        sitesId = sitesId,
                        processRouteId = ProcessRouteId,
                        processStepId = ProcessStepId,
                        planQuantity = PlanQuantity,
                        startTime = StartTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        endTime = EndTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        taskColor = TaskColorHex,
                        workStation = 0,
                        workOrderID = WorkOrderId,
                    };

                    var json = JsonSerializer.Serialize(taskData);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    // 修改URL格式，使用查询参数而不是路径参数
                    var url = $"http://localhost:5005/api/task/updTask?taskId={TaskId}";
                    System.Diagnostics.Debug.WriteLine($"编辑任务URL: {url}");
                    System.Diagnostics.Debug.WriteLine($"编辑任务TaskId: '{TaskId}'");
                    System.Diagnostics.Debug.WriteLine($"编辑任务发送的JSON: {json}");

                    var response = await client.PutAsync(url, content);

                    if (response.IsSuccessStatusCode)
                    {
                        System.Windows.MessageBox.Show("任务更新成功！", "成功");
                        DialogClosed?.Invoke(true);
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"编辑失败响应状态码: {response.StatusCode}");
                        System.Diagnostics.Debug.WriteLine($"编辑失败响应内容: {errorContent}");
                        System.Windows.MessageBox.Show($"更新失败：{response.StatusCode}\n详细信息：{errorContent}", "错误");
                    }
                }
                else
                {
                    var taskData = new
                    {
                        taskNumber = TaskCode,
                        taskName = TaskName,
                        sitesId = sitesId,  // 直接使用变量
                        processRouteId = ProcessRouteId,
                        processStepId = ProcessStepId,
                        planQuantity = PlanQuantity,
                        startTime = StartTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        endTime = EndTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        taskColor = TaskColorHex,
                        workStation = 0,
                        workOrderId = WorkOrderId,
                    };

                    var json = JsonSerializer.Serialize(taskData);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    System.Diagnostics.Debug.WriteLine($"最终发送的JSON: {json}");

                    var response = await client.PostAsync("http://localhost:5005/api/task/task", content);

                    if (response.IsSuccessStatusCode)
                    {
                        System.Windows.MessageBox.Show("任务添加成功！", "成功");
                        DialogClosed?.Invoke(true);
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        System.Windows.MessageBox.Show($"添加失败：{errorContent}", "错误");
                    }
                }
                
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"添加任务时发生错误：{ex.Message}", "错误");
            }
        }


        //站点选择
        public ObservableCollection<SiteModel> SiteOptions { get; } = new ObservableCollection<SiteModel>();

        public SiteModel SelectedSite
        {
            get => _selectedSite;
            set
            {
                _selectedSite = value;
                SiteCode = value?.SiteCode ?? "";
                OnPropertyChanged(nameof(SelectedSite));

                //调试输出
                System.Diagnostics.Debug.WriteLine($"选择站点：ID={value?.SitesId}，名称={value?.SiteName},编号={value?.SiteCode}");
            }
        }

        public string SiteCode
        {
            get => _siteCode;
            set { _siteCode = value; OnPropertyChanged(nameof(SiteCode)); }
        }

        public string TaskColorHex
        {
            get => _taskColorHex;
            set
            {
                _taskColorHex = value;
                OnPropertyChanged(nameof(TaskColorHex));
                OnPropertyChanged(nameof(TaskColor));
            }
        }

        public Brush TaskColor
        {
            get
            {
                try
                {
                    return new SolidColorBrush((Color)ColorConverter.ConvertFromString(TaskColorHex));
                }
                catch
                {
                    return new SolidColorBrush(Colors.Blue);
                }
            }
        }

        private async void LoadSites()
        {
            try
            {
                using var client = new HttpClient();
                if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
                {
                    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
                }

                var response = await client.GetAsync("http://localhost:5005/api/task/sites");

                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"站点API返回: {json}");

                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var result = JsonSerializer.Deserialize<SiteApiResponse>(json, options);

                    if (result?.Code == 200 && result.Result != null)
                    {
                        SiteOptions.Clear();
                        foreach (var site in result.Result)
                        {
                            SiteOptions.Add(new SiteModel
                            {
                                SitesId = site.Id,
                                SiteName = site.SiteName,
                                SiteCode = site.SiteCode
                            });
                        }

                        System.Diagnostics.Debug.WriteLine($"成功加载了 {SiteOptions.Count} 个站点");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"API返回错误: Code={result?.Code}, Message={result?.Message}");
                        //LoadDefaultSites();
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"HTTP请求失败: {response.StatusCode}");
                    //LoadDefaultSites();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载站点列表异常: {ex.Message}");
                //LoadDefaultSites();
            }
        }

        //private void LoadDefaultSites()
        //{
        //    SiteOptions.Clear();
        //    SiteOptions.Add(new SiteModel { SiteId = "1", SiteName = "焊接站", SiteCode = "ZD001" });
        //    SiteOptions.Add(new SiteModel { SiteId = "2", SiteName = "装配站", SiteCode = "ZD002" });
        //    SiteOptions.Add(new SiteModel { SiteId = "3", SiteName = "检测站", SiteCode = "ZD003" });
        //}

        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));

        public event NotifyCollectionChangedEventHandler? CollectionChanged;
    }

    // 站点API响应模型
    public class SiteApiResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public List<SiteApiModel> Result { get; set; }
    }

    public class SiteApiModel
    {
        public string SiteCode { get; set; }
        public string SiteName { get; set; }
        public string SiteType { get; set; }
        public string WorkshopId { get; set; }
        public string ProductionLineId { get; set; }
        public string ProcessId { get; set; }
        public string SiteAddress { get; set; }
        public bool State { get; set; }
        public string SiteDesc { get; set; }
        public string Remark { get; set; }
        public object SiteEquipments { get; set; }
        public object SiteTools { get; set; }
        public object Positions { get; set; }
        public bool IsDelete { get; set; }
        public string CreateTime { get; set; }
        public string? UpdateTime { get; set; }
        public string CreateUserId { get; set; }
        public string CreateUserName { get; set; }
        public string UpdateUserId { get; set; }
        public string UpdateUserName { get; set; }
        public string Id { get; set; }
    }

    public class SiteModel
    {
        public string SitesId { get; set; }
        public string SiteName { get; set; }
        public string SiteCode { get; set; }
    }
}
