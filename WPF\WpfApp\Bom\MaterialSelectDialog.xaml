<Window x:Class="WpfApp.Bom.MaterialSelectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:WpfApp.Bom"
        Title="添加物料配件" Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- 选项卡背景颜色转换器 -->
        <local:BooleanToTabBackgroundConverter x:Key="BooleanToTabBackgroundConverter"/>

        <!-- 选项卡文字颜色转换器 -->
        <local:BooleanToTabTextColorConverter x:Key="BooleanToTabTextColorConverter"/>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="添加物料配件" FontSize="18" FontWeight="Bold"
                   Margin="0,0,0,20" Foreground="#333" HorizontalAlignment="Center"/>

        <!-- 选项卡 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="20,10">
            <Border Name="MaterialTab"
                    Background="{Binding IsMaterialTabSelected, Converter={StaticResource BooleanToTabBackgroundConverter}}"
                    CornerRadius="5,5,0,0" Padding="15,8" Cursor="Hand">
                <Border.InputBindings>
                    <MouseBinding MouseAction="LeftClick" Command="{Binding SelectMaterialTabCommand}"/>
                </Border.InputBindings>
                <TextBlock Text="物料"
                           Foreground="{Binding IsMaterialTabSelected, Converter={StaticResource BooleanToTabTextColorConverter}}"
                           FontWeight="Bold"/>
            </Border>
            <Border Name="ProductTab"
                    Background="{Binding IsProductTabSelected, Converter={StaticResource BooleanToTabBackgroundConverter}}"
                    CornerRadius="5,5,0,0" Padding="15,8" Margin="2,0,0,0" Cursor="Hand">
                <Border.InputBindings>
                    <MouseBinding MouseAction="LeftClick" Command="{Binding SelectProductTabCommand}"/>
                </Border.InputBindings>
                <TextBlock Text="产品"
                           Foreground="{Binding IsProductTabSelected, Converter={StaticResource BooleanToTabTextColorConverter}}"
                           FontWeight="Bold"/>
            </Border>
        </StackPanel>

        <!-- 物料列表 -->
        <Grid Grid.Row="2">
            <!-- 加载指示器 -->
            <Border Background="White" Opacity="0.8" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                    Panel.ZIndex="1">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="正在加载..." FontSize="14" Foreground="#666" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                <DataGrid x:Name="materialDataGrid"
                      ItemsSource="{Binding Materials}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      SelectionMode="Extended"
                      GridLinesVisibility="All"
                      HeadersVisibility="All"
                      RowHeaderWidth="0"
                      MinHeight="300">
                
                <DataGrid.Columns>
                    <!-- 多选框列 -->
                    <DataGridTemplateColumn Width="50">
                        <DataGridTemplateColumn.Header>
                            <CheckBox x:Name="selectAllCheckBox" 
                                      IsChecked="{Binding DataContext.IsAllSelected, RelativeSource={RelativeSource AncestorType=Window}}"
                                      Command="{Binding DataContext.SelectAllCommand, RelativeSource={RelativeSource AncestorType=Window}}"/>
                        </DataGridTemplateColumn.Header>
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                          HorizontalAlignment="Center"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- 序号列 -->
                    <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="RowNumber"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <!-- 物料编号列 -->
                    <DataGridTextColumn Header="物料编号" Width="120" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="MaterialCode"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <!-- 物料名称列 -->
                    <DataGridTextColumn Header="物料名称" Width="150" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="MaterialName"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <!-- 规格型号列 -->
                    <DataGridTextColumn Header="规格型号" Width="120" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="Specification"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <!-- 单位列 -->
                    <DataGridTextColumn Header="单位" Width="80" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="Unit"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <!-- 物料类型列 -->
                    <DataGridTextColumn Header="物料类型" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="MaterialType"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <!-- 物料属性列 -->
                    <DataGridTextColumn Header="物料属性" Width="100" IsReadOnly="True">
                        <DataGridTextColumn.Binding>
                            <Binding Path="MaterialAttribute"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
            </ScrollViewer>
        </Grid>

        <!-- 分页和按钮区域 -->
        <Grid Grid.Row="3" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 分页信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="共 " Foreground="#666"/>
                <TextBlock Text="{Binding TotalCount}" Foreground="#666"/>
                <TextBlock Text=" 条" Foreground="#666"/>

                <Button Content="&lt;" Width="30" Height="30" Margin="20,0,5,0"
                        Command="{Binding PreviousPageCommand}"
                        Background="White" BorderBrush="#DDD" BorderThickness="1"/>

                <TextBlock Text="{Binding CurrentPage}" VerticalAlignment="Center" Margin="10,0"/>

                <Button Content="&gt;" Width="30" Height="30" Margin="5,0,20,0"
                        Command="{Binding NextPageCommand}"
                        Background="White" BorderBrush="#DDD" BorderThickness="1"/>

                <TextBlock Text="前往" VerticalAlignment="Center" Margin="0,0,5,0" Foreground="#666"/>
                <TextBox Width="50" Height="25" Text="{Binding GoToPage, UpdateSourceTrigger=PropertyChanged}"
                         VerticalContentAlignment="Center" HorizontalContentAlignment="Center">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding GoToPageCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>
                <TextBlock Text="页" VerticalAlignment="Center" Margin="5,0,10,0" Foreground="#666"/>
                <Button Content="跳转" Width="40" Height="25"
                        Command="{Binding GoToPageCommand}"
                        Background="#1976D2" BorderThickness="0"
                        Foreground="White" FontSize="11"/>
            </StackPanel>

            <!-- 操作按钮 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="取消" Width="80" Height="35" Margin="0,0,15,0"
                        Background="White" BorderBrush="#DDD" BorderThickness="1"
                        Foreground="#666" FontWeight="Medium"
                        Click="Cancel_Click"/>
                <Button Content="确定" Width="80" Height="35"
                        Background="#1976D2" BorderThickness="0"
                        Foreground="White" FontWeight="Medium"
                        Click="Confirm_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
