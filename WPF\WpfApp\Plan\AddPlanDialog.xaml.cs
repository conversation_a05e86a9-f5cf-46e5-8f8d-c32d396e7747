using System.Windows.Controls;

namespace WpfApp.Plan
{
    /// <summary>
    /// AddPlanDialog.xaml 的交互逻辑
    /// </summary>
    public partial class AddPlanDialog : UserControl
    {
        public AddPlanDialog()
        {
            InitializeComponent();
            // 不要设置 DataContext，让它继承父级
        }

         private void UploadAttachment_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            // 调用PlanViewModel的UploadAttachment方法
            if (this.DataContext is PlanViewModel vm)
            {
                vm.UploadAttachmentCommand?.Execute(null);
            }
        }
    }
} 