<Window x:Class="WpfApp.ProductSelectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="选择产品" Height="500" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        MinHeight="400" MinWidth="600"
        Background="#F5F5F5">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="选择产品" FontSize="18" FontWeight="Bold"
                   Margin="0,0,0,20" Foreground="#333"/>



        <!-- 产品列表 -->
        <Border Grid.Row="1" Background="White" CornerRadius="5" BorderBrush="#E0E0E0" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 表头 -->
                <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                    <Grid Height="40">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50"/>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="选择" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="1" Text="序号" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="2" Text="产品编号" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="3" Text="产品名称" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="4" Text="规格型号" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="5" Text="单位" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="6" Text="产品类型" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                        <TextBlock Grid.Column="7" Text="产品属性" HorizontalAlignment="Center" VerticalAlignment="Center" FontWeight="Medium"/>
                    </Grid>
                </Border>

                <!-- 数据列表 -->
                <DataGrid Grid.Row="1" x:Name="productDataGrid"
                          ItemsSource="{Binding Products}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          CanUserReorderColumns="False"
                          CanUserResizeRows="False"
                          HeadersVisibility="None"
                          GridLinesVisibility="Horizontal"
                          HorizontalGridLinesBrush="#F0F0F0"
                          VerticalGridLinesBrush="Transparent"
                          Background="White"
                          BorderThickness="0"
                          SelectionMode="Single"
                          SelectionUnit="FullRow">

                    <DataGrid.Columns>
                        <!-- 单选框列 -->
                        <DataGridTemplateColumn Width="50" IsReadOnly="False">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <RadioButton HorizontalAlignment="Center" VerticalAlignment="Center"
                                                 GroupName="ProductSelection"
                                                 IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                                 Command="{Binding DataContext.SelectProductCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                 CommandParameter="{Binding}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTextColumn Width="60" Binding="{Binding Index}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Width="120" Binding="{Binding ProductCode}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Width="*" Binding="{Binding ProductName}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10,5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Width="100" Binding="{Binding Specification}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Width="80" Binding="{Binding Unit}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Width="100" Binding="{Binding ProductType}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Width="80" Binding="{Binding ProductAttribute}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>


            </Grid>
        </Border>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="取消" Command="{Binding CancelCommand}"
                    Width="80" Height="35" Margin="0,0,10,0"
                    Background="White" BorderBrush="#DDD" BorderThickness="1"
                    Foreground="#666" FontWeight="Medium"/>
            <Button Content="确定" Command="{Binding ConfirmCommand}"
                    Width="80" Height="35"
                    Background="#1976D2" BorderThickness="0"
                    Foreground="White" FontWeight="Medium"/>
        </StackPanel>
    </Grid>
</Window>
