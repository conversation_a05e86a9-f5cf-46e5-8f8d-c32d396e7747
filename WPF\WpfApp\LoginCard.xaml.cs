using System.Windows.Controls;
using System.Windows;

namespace WpfApp
{
    public partial class LoginCard : UserControl
    {
        private MainWindow _mainWindow;
        public LoginCard(MainWindow mainWindow)
        {
            InitializeComponent();
            _mainWindow = mainWindow;
            var vm = new MainWindowViewModel();
            vm.LoginSucceeded += OnLoginSucceeded;
            this.DataContext = vm;
        }

        private void OnLoginSucceeded()
        {
            _mainWindow.ShowMenuPage();
        }
    }
} 