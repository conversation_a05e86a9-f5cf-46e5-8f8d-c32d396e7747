# BOM字段值缺失问题修复总结

## 问题描述
在BOM保存过程中，调试器显示`dto`对象中有几个关键字段没有值：
- `ProductId`: 0
- `ProcessRouteId`: 0  
- `MaterialIDs`: Count = 0
- `MaterialIdsw`: Count = 0
- `ProcessStepId`: 0
- `State`: 0

这些字段的缺失导致BOM数据不完整，影响后端处理。

## 问题原因分析

### 1. ProductId字段缺失
- 产品选择时没有正确设置ProductId
- 或者ProductId为0时没有通过ProductCode获取对应的ID

### 2. ProcessRouteId字段缺失
- 工艺路线选择时没有正确设置ProcessRouteId
- 或者ProcessRouteId为空时没有通过ProcessRouteCode获取对应的ID

### 3. MaterialIDs字段为空
- 物料选择后ID没有正确传递到保存对象中
- UpdateMaterialIDs方法可能没有正确执行

### 4. State字段为0
- 没有设置默认状态值
- 状态字段应该有一个有效的默认值

## 修复内容

### 1. 增强SaveBomToApi方法
**文件**: `WpfApp/Bom/BomEditViewModel.cs`

#### 添加字段值获取逻辑
```csharp
// 获取ProductId（如果ProductId为0但有ProductCode，尝试从API获取）
int productId = ProductId;
if (productId == 0 && !string.IsNullOrEmpty(ProductCode))
{
    productId = await GetProductIdByCode(ProductCode);
}

// 获取ProcessRouteId（如果ProcessRouteId为空但有SelectedProcessRouteCode，尝试从API获取）
int processRouteId = 0;
if (string.IsNullOrEmpty(ProcessRouteId) && !string.IsNullOrEmpty(SelectedProcessRouteCode))
{
    processRouteId = await GetProcessRouteIdByCode(SelectedProcessRouteCode);
}
```

#### 完善BomCreateDto构建
```csharp
var bomDto = new BomCreateDto
{
    BomCode = BomCode,
    BomVersion = BomVersion,
    ProductId = productId, // 设置ProductId
    ProductCode = ProductCode,
    Specification = Specification,
    Unit = Unit,
    DailyOutput = DailyOutput,
    Remarks = Remarks,
    State = State > 0 ? State : 1, // 设置默认状态为1
    ProcessRouteId = processRouteId, // 设置ProcessRouteId
    ProcessStepId = ProcessStepId,
    MaterialIds = MaterialIDs?.ToArray() ?? new int[0],
    MaterialIdsw = new int[0]
};
```

### 2. 添加辅助方法

#### GetProductIdByCode方法
```csharp
private async Task<int> GetProductIdByCode(string productCode)
{
    try
    {
        var url = $"http://localhost:5005/api/bom/getProductByCode/{productCode}";
        var response = await _httpClient.GetAsync(url);
        
        if (response.IsSuccessStatusCode)
        {
            var jsonContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ProductApiResponse>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            if (apiResponse?.Result?.Items?.Any() == true)
            {
                var product = apiResponse.Result.Items.First();
                return (int)product.Id;
            }
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"获取ProductId失败: {ex.Message}");
    }
    
    return 0;
}
```

#### GetProcessRouteIdByCode方法
```csharp
private async Task<int> GetProcessRouteIdByCode(string processRouteCode)
{
    try
    {
        var url = $"http://localhost:5005/api/bom/getProcessRouteByCode/{processRouteCode}";
        var response = await _httpClient.GetAsync(url);
        
        if (response.IsSuccessStatusCode)
        {
            var jsonContent = await response.Content.ReadAsStringAsync();
            var apiResponse = JsonSerializer.Deserialize<ProcessRouteApiResponse>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            if (apiResponse?.Result?.Items?.Any() == true)
            {
                var processRoute = apiResponse.Result.Items.First();
                return (int)processRoute.Id;
            }
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"获取ProcessRouteId失败: {ex.Message}");
    }
    
    return 0;
}
```

### 3. 更新BomCreateDto类
**文件**: `WpfApp/Bom/BomEditViewModel.cs`

添加缺失的字段：
```csharp
public class BomCreateDto
{
    public string BomCode { get; set; } = string.Empty;
    public string BomVersion { get; set; } = string.Empty;
    public int ProductId { get; set; } // 添加ProductId字段
    public string ProductCode { get; set; } = string.Empty;
    public string Specification { get; set; } = string.Empty;
    public string Unit { get; set; } = string.Empty;
    public int DailyOutput { get; set; }
    public string Remarks { get; set; } = string.Empty;
    public int State { get; set; }
    public int ProcessRouteId { get; set; } // 添加ProcessRouteId字段
    public int ProcessStepId { get; set; } // 添加ProcessStepId字段
    public int[] MaterialIds { get; set; } = new int[0];
    public int[] MaterialIdsw { get; set; } = new int[0];
}
```

### 4. 添加API响应数据模型
**文件**: `WpfApp/Bom/BomEditViewModel.cs`

```csharp
// 产品API响应数据模型
public class ProductApiResponse
{
    public string? Type { get; set; }
    public string? Message { get; set; }
    public ProductPageResult? Result { get; set; }
}

// 工艺路线API响应数据模型
public class ProcessRouteApiResponse
{
    public string? Type { get; set; }
    public string? Message { get; set; }
    public ProcessRoutePageResult? Result { get; set; }
}

// 工艺路线分页结果
public class ProcessRoutePageResult
{
    public int PageIndex { get; set; }
    public int Total { get; set; }
    public int TotalPages { get; set; }
    public List<ProcessRouteApiItem>? Items { get; set; }
}

// 工艺路线API数据项
public class ProcessRouteApiItem
{
    public long Id { get; set; }
    public string? ProcessRouteCode { get; set; }
    public string? ProcessRouteName { get; set; }
    public string? Remarks { get; set; }
}
```

### 5. 增强调试日志
在SaveBomToApi方法中添加详细的调试信息：
```csharp
Console.WriteLine($"=== 准备保存BOM ===");
Console.WriteLine($"BOM编码: {bomDto.BomCode}");
Console.WriteLine($"产品编码: {bomDto.ProductCode}");
Console.WriteLine($"产品ID: {bomDto.ProductId}");
Console.WriteLine($"工艺路线ID: {bomDto.ProcessRouteId}");
Console.WriteLine($"状态: {bomDto.State}");
Console.WriteLine($"物料数量: {bomDto.MaterialIds.Length}");
if (bomDto.MaterialIds.Length > 0)
{
    Console.WriteLine($"物料IDs: [{string.Join(", ", bomDto.MaterialIds)}]");
}
```

## 修复效果

### 修复前
- ProductId: 0
- ProcessRouteId: 0
- MaterialIDs: Count = 0
- State: 0
- 保存时关键字段缺失

### 修复后
- ProductId: 通过ProductCode自动获取或使用已设置的ID
- ProcessRouteId: 通过ProcessRouteCode自动获取或使用已设置的ID
- MaterialIDs: 正确包含选中的物料ID数组
- State: 默认设置为1，确保有有效值
- 保存时所有关键字段都有正确的值

## 测试建议

1. **产品选择测试**
   - 选择产品后验证ProductId是否正确设置
   - 如果ProductId为0，验证是否能通过ProductCode获取

2. **工艺路线选择测试**
   - 选择工艺路线后验证ProcessRouteId是否正确设置
   - 如果ProcessRouteId为空，验证是否能通过ProcessRouteCode获取

3. **物料选择测试**
   - 选择物料后验证MaterialIDs数组是否包含正确的ID
   - 验证UpdateMaterialIDs方法是否正确执行

4. **保存功能测试**
   - 填写完整的BOM信息后保存
   - 查看调试日志，确认所有字段都有正确的值
   - 验证API请求中的JSON数据是否完整

## 注意事项

1. **API端点**
   - 确保后端提供了`/api/bom/getProductByCode/{productCode}`端点
   - 确保后端提供了`/api/bom/getProcessRouteByCode/{processRouteCode}`端点

2. **错误处理**
   - 如果API调用失败，会使用默认值0
   - 添加了异常处理和调试日志

3. **数据一致性**
   - 确保前端和后端的数据模型一致
   - 注意字段类型转换（long转int）

## 相关文件清单

- `WpfApp/Bom/BomEditViewModel.cs` - 主要修复文件
- `WpfApp/Bom/MaterialSelectViewModel.cs` - 物料选择相关
- `WpfApp/Bom/ProductSelectDialog.xaml.cs` - 产品选择对话框
- `WpfApp/Bom/ProcessRouteSelectDialog.xaml.cs` - 工艺路线选择对话框 