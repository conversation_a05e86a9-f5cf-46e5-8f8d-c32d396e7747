using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Input;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;

namespace WpfApp.Bom
{
    /// <summary>
    /// 工艺路线选择ViewModel
    /// </summary>
    public class ProcessRouteSelectViewModel : INotifyPropertyChanged, IDisposable
    {
        private ObservableCollection<ProcessRouteItem> _processRoutes;
        private ProcessRouteItem _selectedProcessRoute;
        private int _currentPage = 1;
        private int _pageSize = 11;
        private int _totalPages = 0;
        private bool _isLoading = false;
        private bool _hasMorePages = true;
        private readonly HttpClient _httpClient;

        public ProcessRouteSelectViewModel()
        {
            ProcessRoutes = new ObservableCollection<ProcessRouteItem>();
            _httpClient = new HttpClient();

            ConfirmCommand = new RelayCommand(_ => Confirm());
            CancelCommand = new RelayCommand(_ => Cancel());
            PreviousPageCommand = new RelayCommand(_ => PreviousPage(), _ => CanPreviousPage());
            NextPageCommand = new RelayCommand(_ => NextPage(), _ => CanNextPage());

            _ = LoadProcessRoutesAsync();
        }

        /// <summary>
        /// 工艺路线列表
        /// </summary>
        public ObservableCollection<ProcessRouteItem> ProcessRoutes
        {
            get => _processRoutes;
            set { _processRoutes = value; OnPropertyChanged(nameof(ProcessRoutes)); }
        }

        /// <summary>
        /// 选中的工艺路线
        /// </summary>
        public ProcessRouteItem SelectedProcessRoute
        {
            get => _selectedProcessRoute;
            set
            {
                // 先清除之前的选择
                if (_selectedProcessRoute != null)
                    _selectedProcessRoute.IsSelected = false;

                _selectedProcessRoute = value;

                // 设置新的选择
                if (_selectedProcessRoute != null)
                    _selectedProcessRoute.IsSelected = true;

                OnPropertyChanged(nameof(SelectedProcessRoute));
            }
        }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage
        {
            get => _currentPage;
            set { _currentPage = value; OnPropertyChanged(nameof(CurrentPage)); OnPropertyChanged(nameof(PageInfo)); }
        }

        /// <summary>
        /// 页面大小
        /// </summary>
        public int PageSize
        {
            get => _pageSize;
            set { _pageSize = value; OnPropertyChanged(nameof(PageSize)); }
        }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages
        {
            get => _totalPages;
            set { _totalPages = value; OnPropertyChanged(nameof(TotalPages)); OnPropertyChanged(nameof(PageInfo)); }
        }

        /// <summary>
        /// 页面信息
        /// </summary>
        public string PageInfo => $"第 {CurrentPage} 页，共 {TotalPages} 页";

        /// <summary>
        /// 是否有更多页面
        /// </summary>
        public bool HasMorePages
        {
            get => _hasMorePages;
            set { _hasMorePages = value; OnPropertyChanged(nameof(HasMorePages)); }
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set { _isLoading = value; OnPropertyChanged(nameof(IsLoading)); }
        }

        public ICommand ConfirmCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }

        public event Action<ProcessRouteItem> ProcessRouteSelected;
        public event Action CloseRequested;
        public event PropertyChangedEventHandler PropertyChanged;

        private async Task LoadProcessRoutesAsync()
        {
            try
            {
                IsLoading = true;
                ProcessRoutes.Clear();

                // 构建API请求URL
                var url = $"http://localhost:5005/api/bom/pageProcessRoute?Page={CurrentPage}&pageSize={PageSize}";

                System.Diagnostics.Debug.WriteLine($"请求工艺路线API: {url}");

                // 发送HTTP请求
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"API响应: {jsonContent}");

                    try
                    {
                        var apiResponse = JsonSerializer.Deserialize<ProcessRouteApiResponse>(jsonContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        System.Diagnostics.Debug.WriteLine($"API响应解析成功");
                        System.Diagnostics.Debug.WriteLine($"Code: {apiResponse?.Code}");
                        System.Diagnostics.Debug.WriteLine($"Message: {apiResponse?.Message}");
                        System.Diagnostics.Debug.WriteLine($"Result不为空: {apiResponse?.Result != null}");

                        if (apiResponse?.Result?.Items != null)
                        {
                            var result = apiResponse.Result;
                            System.Diagnostics.Debug.WriteLine($"TotalPages: {result.TotalPages}");
                            System.Diagnostics.Debug.WriteLine($"Items数量: {result.Items.Length}");
                            System.Diagnostics.Debug.WriteLine($"Total: {result.Total}");

                            // 转换API数据到UI模型
                            var items = result.Items.Select((item, index) => 
                            {
                                var processRouteItem = new ProcessRouteItem
                                {
                                    Id = item.Id ?? string.Empty,
                                    RowNumber = (CurrentPage - 1) * PageSize + index + 1,
                                    ProcessRouteName = item.ProcessRouteName ?? string.Empty,
                                    ProcessRouteCode = item.ProcessRouteCode ?? string.Empty,
                                    Description = item.Description ?? string.Empty,
                                    IsSystemCode = item.IsSystemCode,
                                    ProcessRouteName2 = item.ProcessRouteName2 ?? string.Empty,
                                    Remarks = item.Remarks ?? string.Empty,
                                    CreateTime = item.CreateTime ?? string.Empty,
                                    UpdateTime = item.UpdateTime ?? string.Empty,
                                    CreatedUserId = item.CreatedUserId ?? string.Empty,
                                    CreatedUserName = item.CreatedUserName ?? string.Empty,
                                    UpdatedUserId = item.UpdatedUserId ?? string.Empty,
                                    UpdatedUserName = item.UpdatedUserName ?? string.Empty
                                };
                                
                                System.Diagnostics.Debug.WriteLine($"创建工艺路线项: ID={processRouteItem.Id}, 名称={processRouteItem.ProcessRouteName}, 编码={processRouteItem.ProcessRouteCode}");
                                return processRouteItem;
                            }).ToList();

                            ProcessRoutes.Clear();
                            foreach (var item in items)
                            {
                                ProcessRoutes.Add(item);
                            }

                            // 更新分页信息
                            TotalPages = result.TotalPages;
                            HasMorePages = CurrentPage < TotalPages;

                            System.Diagnostics.Debug.WriteLine($"加载了 {items.Count} 条工艺路线数据，当前页: {CurrentPage}，总页数: {TotalPages}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("API响应中没有Result.Items数据");
                            if (apiResponse?.Result == null)
                            {
                                System.Diagnostics.Debug.WriteLine("Result为null");
                            }
                            ProcessRoutes.Clear();
                        }
                    }
                    catch (JsonException jsonEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"JSON反序列化失败: {jsonEx.Message}");
                        System.Diagnostics.Debug.WriteLine($"原始JSON: {jsonContent}");
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"API请求失败: {response.StatusCode} - {response.ReasonPhrase}");
                    System.Diagnostics.Debug.WriteLine($"错误内容: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载工艺路线数据时出错: {ex.Message}");

                // 如果API调用失败，显示错误信息或使用备用数据
                // 这里可以添加用户友好的错误处理
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void PreviousPage()
        {
            if (CanPreviousPage())
            {
                CurrentPage--;
                await LoadProcessRoutesAsync();
            }
        }

        private async void NextPage()
        {
            if (CanNextPage())
            {
                CurrentPage++;
                await LoadProcessRoutesAsync();
            }
        }

        private bool CanPreviousPage()
        {
            return CurrentPage > 1;
        }

        private bool CanNextPage()
        {
            return HasMorePages;
        }

        private void Confirm()
        {
            var selectedItem = ProcessRouteItem.GetCurrentSelected();

            System.Diagnostics.Debug.WriteLine($"=== 工艺路线选择对话框确定 ===");
            System.Diagnostics.Debug.WriteLine($"选中的项目: {selectedItem != null}");

            if (selectedItem != null)
            {
                System.Diagnostics.Debug.WriteLine($"选中项目ID: {selectedItem.Id}");
                System.Diagnostics.Debug.WriteLine($"选中项目名称: {selectedItem.ProcessRouteName}");
                System.Diagnostics.Debug.WriteLine($"选中项目编号: {selectedItem.ProcessRouteCode}");

                // 先触发选择事件，这会设置DialogResult = true并关闭对话框
                ProcessRouteSelected?.Invoke(selectedItem);
                System.Diagnostics.Debug.WriteLine("ProcessRouteSelected 事件已触发");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("警告：没有选中任何工艺路线");
                // 没有选择时，触发关闭事件
                CloseRequested?.Invoke();
                System.Diagnostics.Debug.WriteLine("CloseRequested 事件已触发");
            }
        }

        private void Cancel()
        {
            CloseRequested?.Invoke();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// 工艺路线项
    /// </summary>
    public class ProcessRouteItem : INotifyPropertyChanged
    {
        private bool _isSelected;
        private static ProcessRouteItem _currentSelected;

        public string Id { get; set; }
        public int RowNumber { get; set; }
        public string ProcessRouteName { get; set; }
        public string ProcessRouteCode { get; set; }
        public string Description { get; set; }
        public bool IsSystemCode { get; set; }
        public string ProcessRouteName2 { get; set; }
        public string Remarks { get; set; }
        public string CreateTime { get; set; }
        public string UpdateTime { get; set; }
        public string CreatedUserId { get; set; }
        public string CreatedUserName { get; set; }
        public string UpdatedUserId { get; set; }
        public string UpdatedUserName { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;

                    if (value)
                    {
                        // 取消之前选中的项
                        if (_currentSelected != null && _currentSelected != this)
                        {
                            _currentSelected._isSelected = false;
                            _currentSelected.OnPropertyChanged(nameof(IsSelected));
                        }
                        _currentSelected = this;
                    }
                    else if (_currentSelected == this)
                    {
                        _currentSelected = null;
                    }

                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        public static ProcessRouteItem GetCurrentSelected()
        {
            return _currentSelected;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// API响应模型 - 外层响应
    /// </summary>
    public class ProcessRouteApiResponse
    {
        public int Code { get; set; }
        public string Message { get; set; }
        public ProcessRouteResult Result { get; set; }
    }

    /// <summary>
    /// API响应模型 - 结果数据
    /// </summary>
    public class ProcessRouteResult
    {
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int Total { get; set; }
        public int TotalPages { get; set; }
        public ProcessRouteApiItem[] Items { get; set; }
    }

    /// <summary>
    /// API返回的工艺路线项
    /// </summary>
    public class ProcessRouteApiItem
    {
        public string ProcessRouteCode { get; set; }
        public bool IsSystemCode { get; set; }
        public string ProcessRouteName { get; set; }
        public string ProcessRouteName2 { get; set; }
        public string Description { get; set; }
        public string Remarks { get; set; }
        public string CreateTime { get; set; }
        public string UpdateTime { get; set; }
        public string CreatedUserId { get; set; }
        public string CreatedUserName { get; set; }
        public string UpdatedUserId { get; set; }
        public string UpdatedUserName { get; set; }
        public string Id { get; set; }
    }
}
